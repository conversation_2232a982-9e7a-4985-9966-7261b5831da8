import { request } from '../request';

/**
 * 上传115目录树文件
 *
 * @param file 要上传的文件
 */
export function uploadDirectoryTree(file: File) {
  const formData = new FormData();
  formData.append('file', file);

  return request<StrmAPI.UploadResult>({
    url: '/strm/upload',
    method: 'post',
    data: formData
  });
}

/**
 * 解析已上传的115目录树文件
 *
 * @param data 包含记录ID的对象
 */
export function parseDirectoryTree(data: { record_id: string | number; file_path?: string }) {
  return request<StrmAPI.ParseResult>({
    url: '/strm/parse',
    method: 'post',
    data
  });
}

/** 获取上传历史 */
export function getUploadHistory(params: Api.Page.PageParams) {
  return request<Api.Page.PageResult<StrmAPI.UploadRecord>>({
    url: '/strm/history',
    method: 'get',
    params
  });
}

/**
 * 删除上传记录
 *
 * @param recordId 记录ID
 */
export function deleteUploadRecord(recordId: string | number) {
  return request<{ message: string }>({
    url: `/strm/history/${recordId}`,
    method: 'delete'
  });
}

/**
 * 获取文件下载链接
 *
 * @param recordId 记录ID
 * @returns 完整的下载URL
 */
export function getDownloadUrl(recordId: string | number): string {
  // 获取基础URL，确保与其他API请求一致
  const baseURL = import.meta.env.VITE_SERVICE_BASE_URL || '';
  return `${baseURL}/strm/download/${recordId}`;
}

/**
 * 获取文件解析结果
 * 
 * @param recordId 记录ID
 * @returns 解析结果
 */
export function getParseResult(recordId: string | number) {
  return request<StrmAPI.ParseResult>({
    url: `/strm/result/${recordId}`,
    method: 'get'
  });
}
