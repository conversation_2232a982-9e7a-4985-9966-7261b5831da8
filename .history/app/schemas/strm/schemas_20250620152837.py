from typing import Optional, List, Any, Dict
from datetime import datetime

from pydantic import BaseModel, Field, HttpUrl, validator

from app.models.strm import ServerType, TaskStatus, FileType, ServerStatus
from app.schemas.form import as_form
from app.schemas.base import PageData


class MediaServerBase(BaseModel):
    """媒体服务器基础模型"""

    name: str = Field(..., description="服务器名称")
    server_type: ServerType = Field(default=ServerType.HTTP, description="服务器类型")
    base_url: str = Field(..., description="服务器基础URL")
    description: Optional[str] = Field(default=None, description="服务器描述")
    auth_required: bool = Field(default=False, description="是否需要认证")
    username: Optional[str] = Field(default=None, description="认证用户名")
    password: Optional[str] = Field(default=None, description="认证密码")
    status: Optional[ServerStatus] = Field(default=ServerStatus.UNKNOWN, description="服务器连接状态")

    class Config:
        from_attributes = True


class MediaServerCreate(MediaServerBase):
    """创建媒体服务器模型"""

    pass


class MediaServerUpdate(BaseModel):
    """更新媒体服务器模型"""

    name: Optional[str] = Field(default=None, description="服务器名称")
    server_type: Optional[ServerType] = Field(default=None, description="服务器类型")
    base_url: Optional[str] = Field(default=None, description="服务器基础URL")
    description: Optional[str] = Field(default=None, description="服务器描述")
    auth_required: Optional[bool] = Field(default=None, description="是否需要认证")
    username: Optional[str] = Field(default=None, description="认证用户名")
    password: Optional[str] = Field(default=None, description="认证密码")
    status: Optional[ServerStatus] = Field(default=None, description="服务器连接状态")


class MediaServerResponse(MediaServerBase):
    """媒体服务器响应模型"""

    id: int = Field(..., description="服务器ID")
    create_time: datetime = Field(..., description="创建时间")
    update_time: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class PathMappingBase(BaseModel):
    """路径映射规则基础模型"""

    name: str = Field(..., description="规则名称")
    source_path: str = Field(..., description="源路径")
    target_path: str = Field(..., description="目标路径")
    server_id: int = Field(..., description="所属服务器ID")
    is_regex: bool = Field(default=False, description="是否使用正则表达式")

    class Config:
        from_attributes = True


class PathMappingCreate(PathMappingBase):
    """创建路径映射规则模型"""

    pass


class PathMappingUpdate(BaseModel):
    """更新路径映射规则模型"""

    name: Optional[str] = Field(default=None, description="规则名称")
    source_path: Optional[str] = Field(default=None, description="源路径")
    target_path: Optional[str] = Field(default=None, description="目标路径")
    server_id: Optional[int] = Field(default=None, description="所属服务器ID")
    is_regex: Optional[bool] = Field(default=None, description="是否使用正则表达式")


class PathMappingResponse(PathMappingBase):
    """路径映射规则响应模型"""

    id: int = Field(..., description="映射规则ID")
    create_time: datetime = Field(..., description="创建时间")
    update_time: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class FilterRuleBase(BaseModel):
    """文件过滤规则基础模型"""

    name: str = Field(..., description="规则名称")
    file_type: Optional[FileType] = Field(default=None, description="文件类型")
    keyword: Optional[str] = Field(default=None, description="关键词")
    path_pattern: Optional[str] = Field(default=None, description="路径模式")
    is_include: bool = Field(default=True, description="是包含规则还是排除规则")

    class Config:
        from_attributes = True


class FilterRuleCreate(FilterRuleBase):
    """创建文件过滤规则模型"""

    pass


class FilterRuleUpdate(BaseModel):
    """更新文件过滤规则模型"""

    name: Optional[str] = Field(default=None, description="规则名称")
    file_type: Optional[FileType] = Field(default=None, description="文件类型")
    keyword: Optional[str] = Field(default=None, description="关键词")
    path_pattern: Optional[str] = Field(default=None, description="路径模式")
    is_include: Optional[bool] = Field(default=None, description="是包含规则还是排除规则")


class FilterRuleResponse(FilterRuleBase):
    """文件过滤规则响应模型"""

    id: int = Field(..., description="过滤规则ID")
    create_time: datetime = Field(..., description="创建时间")
    update_time: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class UploadResult(BaseModel):
    """上传结果模型"""

    filename: str  # 文件名
    path: str  # 存储路径
    record_id: int  # 上传记录ID


class ParseRequest(BaseModel):
    """解析请求模型"""

    record_id: int  # 上传记录ID
    file_path: Optional[str] = None  # 可选的文件路径参数


class UrlUploadRequest(BaseModel):
    """URL上传请求模型"""

    url: str  # 文件URL地址

    @validator("url")
    def validate_url(cls, v):
        if not v or not isinstance(v, str):
            raise ValueError("无效的URL")
        # 添加基本的URL格式验证
        if not v.startswith(("http://", "https://")):
            raise ValueError("URL必须以http://或https://开头")
        return v


class UploadRecord(BaseModel):
    """上传记录模型"""

    id: int
    filename: str
    filesize: int
    status: str
    create_time: datetime
    uploader_id: int
    parse_time: Optional[datetime] = None


class UploadHistoryResponse(BaseModel):
    """上传历史响应模型"""

    total: int
    page: int
    page_size: int
    records: List[UploadRecord]


class StrmTaskCreate(BaseModel):
    """STRM任务创建请求模型"""

    record_id: int = Field(..., description="上传记录ID")
    server_id: int = Field(..., description="媒体服务器ID")
    output_dir: Optional[str] = Field(default=None, description="自定义输出目录")
    name: Optional[str] = Field(default=None, description="自定义任务名称")


class StrmFileDetail(BaseModel):
    """STRM文件详情模型"""

    id: int = Field(..., description="文件ID")
    source_path: str = Field(..., description="源文件路径")
    target_path: str = Field(..., description="生成的STRM文件路径")
    file_type: str = Field(..., description="文件类型")
    file_size: Optional[int] = Field(default=None, description="文件大小(字节)")
    is_success: bool = Field(..., description="是否生成成功")
    error_message: Optional[str] = Field(default=None, description="错误信息")


class StrmTaskDetail(BaseModel):
    """STRM任务详情模型"""

    id: int = Field(..., description="任务ID")
    name: str = Field(..., description="任务名称")
    status: str = Field(..., description="任务状态")
    total_files: int = Field(..., description="总文件数")
    processed_files: int = Field(..., description="已处理文件数")
    success_files: int = Field(..., description="成功生成文件数")
    failed_files: int = Field(..., description="失败文件数")
    progress: int = Field(..., description="进度百分比")
    start_time: Optional[str] = Field(default=None, description="开始时间")
    end_time: Optional[str] = Field(default=None, description="结束时间")
    output_dir: str = Field(..., description="输出目录")
    files: List[StrmFileDetail] = Field(default_factory=list, description="文件列表（最多10条）")
    file_count: int = Field(..., description="文件总数")


class StrmTaskBrief(BaseModel):
    """STRM任务简要信息模型"""

    id: int = Field(..., description="任务ID")
    name: str = Field(..., description="任务名称")
    status: str = Field(..., description="任务状态")
    total_files: int = Field(..., description="总文件数")
    processed_files: int = Field(..., description="已处理文件数")
    success_files: int = Field(..., description="成功生成文件数")
    failed_files: int = Field(..., description="失败文件数")
    progress: int = Field(..., description="进度百分比")
    start_time: Optional[str] = Field(default=None, description="开始时间")
    end_time: Optional[str] = Field(default=None, description="结束时间")


class StrmTaskResponse(BaseModel):
    """STRM任务列表响应模型"""

    total: int = Field(..., description="总数")
    page: int = Field(..., description="页码")
    page_size: int = Field(..., description="每页数量")
    tasks: List[StrmTaskBrief] = Field(..., description="任务列表")


class StrmGenerateResult(BaseModel):
    """STRM生成结果模型"""

    task_id: int = Field(..., description="任务ID")
    name: str = Field(..., description="任务名称")
    status: str = Field(..., description="任务状态")
    result: Dict[str, Any] = Field(..., description="处理结果")


class StrmGenerateResponse(BaseModel):
    """STRM生成结果模型"""

    task_id: int = Field(..., description="任务ID")
    name: str = Field(..., description="任务名称")
    status: str = Field(..., description="任务状态")
    result: Dict[str, Any] = Field(..., description="处理结果")


class SystemSettingsBase(BaseModel):
    """系统设置基础模型"""

    default_server_id: Optional[int] = Field(default=None, description="默认服务器ID（旧版本兼容）")
    default_download_server_id: Optional[int] = Field(default=None, description="默认下载服务器ID")
    default_media_server_id: Optional[int] = Field(default=None, description="默认媒体服务器ID")
    enable_path_replacement: bool = Field(default=True, description="启用路径替换")
    replacement_path: Optional[str] = Field(default="/nas", description="路径替换值")
    download_threads: int = Field(default=1, description="默认下载线程数")
    output_directory: Optional[str] = Field(default=None, description="默认输出目录")

    class Config:
        from_attributes = True


class SystemSettingsUpdate(BaseModel):
    """系统设置更新模型"""

    default_server_id: Optional[int] = Field(default=None, description="默认服务器ID（旧版本兼容）")
    default_download_server_id: Optional[int] = Field(default=None, description="默认下载服务器ID")
    default_media_server_id: Optional[int] = Field(default=None, description="默认媒体服务器ID")
    enable_path_replacement: Optional[bool] = Field(default=None, description="启用路径替换")
    replacement_path: Optional[str] = Field(default=None, description="路径替换值")
    download_threads: Optional[int] = Field(default=None, description="默认下载线程数")
    output_directory: Optional[str] = Field(default=None, description="默认输出目录")


class SystemSettingsResponse(SystemSettingsBase):
    """系统设置响应模型"""

    id: int = Field(..., description="设置ID")
    default_server: Optional[MediaServerResponse] = Field(default=None, description="默认服务器详情（旧版本兼容）")
    default_download_server: Optional[MediaServerResponse] = Field(default=None, description="默认下载服务器详情")
    default_media_server: Optional[MediaServerResponse] = Field(default=None, description="默认媒体服务器详情")
    update_time: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


__all__ = [
    "MediaServerCreate",
    "MediaServerUpdate",
    "MediaServerResponse",
    "PathMappingCreate",
    "PathMappingUpdate",
    "PathMappingResponse",
    "FilterRuleCreate",
    "FilterRuleUpdate",
    "FilterRuleResponse",
    "ParseRequest",
    "UrlUploadRequest",
    "UploadRecord",
    "UploadHistoryResponse",
    "UploadResult",
    "StrmTaskCreate",
    "StrmTaskResponse",
    "StrmTaskDetail",
    "StrmTaskBrief",
    "StrmFileDetail",
    "StrmGenerateResult",
    "StrmGenerateResponse",
    "SystemSettingsBase",
    "SystemSettingsUpdate",
    "SystemSettingsResponse",
]
