import { request } from '../request';

/**
 * 上传115目录树文件
 *
 * @param file 要上传的文件
 */
export function uploadDirectoryTree(file: File) {
  const formData = new FormData();
  formData.append('file', file);

  return request<StrmAPI.UploadResult>({
    url: '/strm/upload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  });
}

/**
 * 解析已上传的115目录树文件
 *
 * @param filePath 已上传文件的路径
 */
export function parseDirectoryTree(filePath: string) {
  return request<StrmAPI.ParseResult>({
    url: '/strm/parse',
    method: 'post',
    data: {
      file_path: filePath
    }
  });
}
