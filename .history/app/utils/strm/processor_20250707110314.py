"""
STRM处理器，用于生成STRM文件
"""

import os
import re
import asyncio
import threading
import queue
import time
import shutil
import zipfile
import logging
import concurrent.futures
from datetime import datetime, timedelta
from typing import List, Dict, Any, Tuple, Optional, Union
from concurrent.futures import ThreadPoolExecutor
from urllib.parse import quote
from tortoise.expressions import Q, F
from tortoise.functions import Sum
from tortoise.transactions import in_transaction
import traceback
from pathlib import Path
import aiofiles
from app.models.strm import MediaServer, FileType, StrmTask, StrmFile, TaskStatus, SystemSettings, ProcessType
from app.models.strm import DownloadTask, DownloadTaskStatus
from app.utils.strm.parser import TreeParser
from app.log.log import log
from app.controllers.strm import system_settings_controller
from types import SimpleNamespace
import httpx


class StrmProcessor:
    """STRM处理器，用于生成STRM文件"""

    def __init__(
        self,
        server: MediaServer,
        output_dir: str,
        enable_path_replacement: bool = False,
        replacement_path: str = "/nas",
        verbose_console_logging: bool = False,
        settings_dict: Optional[Dict[str, Any]] = None,
    ):
        """
        初始化STRM处理器

        Args:
            server: 媒体服务器配置
            output_dir: 输出目录
            enable_path_replacement: 是否启用路径替换
            replacement_path: 路径替换值
            verbose_console_logging: 是否输出详细日志到控制台，默认为False
            settings_dict: 系统设置字典，用于获取文件类型配置
        """
        self.server = server
        self.output_dir = output_dir
        self.base_url = server.base_url
        self.enable_path_replacement = enable_path_replacement
        self.replacement_path = replacement_path
        self.verbose_console_logging = verbose_console_logging
        self.settings_dict = settings_dict

        self.logger = logging.getLogger("strm_processor")

        for handler in self.logger.handlers:
            if isinstance(handler, logging.StreamHandler):
                if not verbose_console_logging:
                    handler.setLevel(logging.ERROR)
                else:
                    handler.setLevel(logging.INFO)

    def replace_base_path(self, original_path: str) -> str:
        """
        替换路径中的基础路径

        Args:
            original_path: 原始路径

        Returns:
            替换后的路径
        """
        if not self.enable_path_replacement:
            return original_path

        if not original_path:
            return original_path

        # 替换路径
        return original_path.replace("/", self.replacement_path, 1) if original_path.startswith("/") else original_path


class ResourceDownloader:
    """资源文件下载器"""

    def __init__(
        self,
        server: MediaServer,
        output_dir: str,
        task_list: Optional[List] = None,
        threads: int = 3,
    ):
        self.server = server
        self.output_dir = Path(output_dir)
        self.threads = threads
        self.task_list = task_list
        self.lock = threading.Lock()
        self.stats = {
            "total_files": 0,
            "success_files": 0,
            "failed_files": 0,
            "total_size": 0,
            "downloaded_size": 0,
            "start_time": time.time(),
            "download_time": 0,
            "average_speed": 0,
        }

    async def add_file(self, file_info: Dict[str, Any], task_id: int):
        """
        添加下载任务

        Args:
            file_info: 文件信息，包含路径、大小、类型等
            task_id: 任务ID
        """
        try:
            task = await StrmTask.get(id=task_id)
            file_type_str = file_info.get("file_type", "other")
            try:
                file_type = FileType(file_type_str)
            except ValueError:
                file_type = FileType.OTHER

            # 创建下载任务记录
            await DownloadTask.create(
                task=task,
                source_path=file_info.get("path", ""),
                file_type=file_type,
                process_type=ProcessType.RESOURCE_DOWNLOAD,
                status=DownloadTaskStatus.PENDING,
                file_size=file_info.get("size", 0),
            )
            return True
        except Exception as e:
            log.error(f"添加下载任务失败: {str(e)}")
            return False

    async def download_worker(self, task):
        try:
            url = self.server.base_url + task.source_path
            log.info(f"📥 正在下载: {url}" + "\n")
            save_path = self.output_dir / Path(task.source_path).relative_to("/")

            # 使用通用函数确保目录存在
            directory = os.path.dirname(save_path)
            if directory:
                self.ensure_dir_exists(directory)

            async with httpx.Client() as client:
                response = client.get(url, follow_redirects=True)
                response.raise_for_status()  # 检查响应状态

                async with aiofiles.open(save_path, "wb") as f:
                    await f.write(response.content)

            log.info(f"✅ 下载成功: 已保存到 {save_path}\n")
            return save_path

        except httpx.HTTPStatusError as e:
            log.error(f"❌ HTTP错误: {e.response.status_code} - {e.response.reason_phrase}")
            raise
        except httpx.RequestError as e:
            log.error(f"❌ 网络请求错误: {str(e)} {url}")
            raise
        except Exception as e:
            log.error(f"❌ 未知错误: {str(e)}")
            raise

    async def start_download(self):
        if not self.task_list:
            log.warning("没有任务需要处理")
            return ""
        total_tasks = len(self.task_list)
        # 分批处理，每批次最多处理max_concurrent个任务
        for i in range(0, total_tasks, self.threads):
            batch = self.task_list[i : i + self.threads]
            # 创建批处理任务
            batch_tasks = [self.process_single_task(task) for task in batch]

            # 并发执行
            await asyncio.gather(*batch_tasks, return_exceptions=True)

    async def process_single_task(self, task) -> bool:
        """
        处理单个STRM任务

        Args:
            task: 单个任务对象

        Returns:
            bool: 处理成功返回True，失败返回False
        """
        try:
            rest = await self.download_worker(task)
            task.status = TaskStatus.COMPLETED
            task.target_path = rest
            await task.save()
            return True
        except Exception as e:
            error_msg = str(e)
            task.error_message = error_msg
            task.status = TaskStatus.FAILED
            await task.save()
            log.error(f"处理任务失败 [path={task.source_path}]: {error_msg}")
            return False

    def ensure_dir_exists(self, directory):
        try:
            if not os.path.exists(directory):
                os.makedirs(directory)
                # print("\n" + Fore.YELLOW + f"📁 创建目录: {directory}")

                return True
            return True
        except Exception as e:
            log.error(f"❌ 创建目录失败: {str(e)}")
            return False


async def process_directory_tree(
    server_id: int,
    files: List[Dict[str, Any]],
    output_dir: str,
    task_id: Optional[int] = None,
    download_server_id: Optional[int] = None,
    threads: int = 1,
    verbose_console_logging: bool = False,
) -> Dict[str, Any]:
    """
    处理文件目录树，生成STRM文件和下载资源文件

    Args:
        server_id: 媒体服务器ID
        files: 文件列表
        output_dir: 输出目录
        task_id: 任务ID
        download_server_id: 下载服务器ID
        threads: 下载线程数
        verbose_console_logging: 是否输出详细日志到控制台

    Returns:
        处理结果
    """
    try:
        # 获取媒体服务器
        server = await MediaServer.get_or_none(id=server_id)
        if not server:
            raise ValueError(f"找不到ID为{server_id}的媒体服务器")

        # 获取下载服务器（如果有）
        download_server = None
        if download_server_id:
            download_server = await MediaServer.get_or_none(id=download_server_id)
            if not download_server:
                raise ValueError(f"找不到ID为{download_server_id}的下载服务器")
        else:
            # 默认使用媒体服务器作为下载服务器
            download_server = server

        # 获取任务
        task = None
        if task_id:
            task = await StrmTask.get_or_none(id=task_id)
            if not task:
                # 添加错误日志
                log.error(f"找不到ID为{task_id}的任务")
                raise ValueError(f"找不到ID为{task_id}的任务")
            else:
                # 添加详细的调试日志
                print(f"[DEBUG] 获取到任务对象: ID={task.id}, 名称={task.name}, 类型={type(task)}")
                print(f"[DEBUG] 任务详情: 状态={task.status}, 总文件数={task.total_files}, 输出目录={task.output_dir}")

        # 获取系统设置
        settings = await system_settings_controller.get_settings()
        settings = SimpleNamespace(**settings)

        # 创建资源下载处理器，用于下载服务器
        resource_processor = StrmProcessor(
            server=download_server,
            output_dir=output_dir,
            enable_path_replacement=settings.enable_path_replacement,
            replacement_path=settings.replacement_path,
            verbose_console_logging=verbose_console_logging,
            settings_dict=settings,
        )

        # 记录开始时间
        start_time = datetime.now()

        # 统计数据
        stats = {
            "total": len(files),
            "processed": 0,
            "success_strm": 0,
            "failed_strm": 0,
            "success_download": 0,
            "failed_download": 0,
            "time": 0,
        }

        # 处理STRM生成任务
        if task_id:
            strm_tasks = await DownloadTask.filter(task__id=task_id, process_type="strm_generation").all()
            strm_processor = strm_downaload(
                server=server,
                output_dir=output_dir,
                enable_path_replacement=settings.enable_path_replacement,
                replacement_path=settings.replacement_path,
                task_list=strm_tasks,
                max_threads=settings.download_threads,
            )
            await strm_processor.genstrm()

        # 处理资源下载任务
        if task_id:
            download_tasks = await DownloadTask.filter(task__id=task_id, process_type="resource_download").all()
            resource_downloader = ResourceDownloader(
                server=download_server,
                output_dir=output_dir,
                task_list=download_tasks,
                threads=settings.download_threads,
            )
            await resource_downloader.start_download()

        # 计算处理时间
        end_time = datetime.now()
        elapsed_time = (end_time - start_time).total_seconds()
        stats["time"] = elapsed_time

        # 更新任务状态为已完成
        if task:
            task.status = TaskStatus.COMPLETED
            task.end_time = end_time
            task.processed_files = stats["processed"]
            task.success_files = stats["success_strm"] + stats["success_download"]
            task.failed_files = stats["failed_strm"] + stats["failed_download"]
            task.download_duration = elapsed_time

            # 生成处理总结
            summary = (
                f"处理完成。总耗时: {elapsed_time:.2f} 秒\n"
                f"总文件数: {stats['total']}\n"
                f"成功生成STRM文件数: {stats['success_strm']}\n"
                f"失败生成STRM文件数: {stats['failed_strm']}\n"
                f"成功下载资源文件数: {stats['success_download']}\n"
                f"失败下载资源文件数: {stats['failed_download']}"
            )
            await task.log(summary, level="INFO")

            await task.save()

        return stats
    except Exception as e:
        # 记录整个处理过程的错误
        log.error(f"处理文件目录树时发生错误: {str(e)}")
        print(f"[ERROR] 处理文件目录树时发生错误: {str(e)}")
        print(f"[ERROR] 异常类型: {type(e)}")
        print(f"[ERROR] 异常堆栈: {traceback.format_exc()}")

        # 更新任务状态为失败
        if task_id:
            task = await StrmTask.get_or_none(id=task_id)
            if task:
                task.status = TaskStatus.FAILED
                await task.log(f"处理文件目录树时发生错误: {str(e)}", level="ERROR")
                await task.save()

        # 重新抛出异常，让上层处理
        raise


def is_summary_log(message: str) -> bool:
    """
    判断是否为汇总日志

    Args:
        message: 日志消息

    Returns:
        是否为汇总日志
    """
    summary_keywords = [
        "总耗时",
        "下载完成",
        "任务统计",
        "总文件数",
        "成功文件数",
        "失败文件数",
        "平均速度",
        "汇总",
        "总计",
        "summary",
        "开始多线程下载",
        "队列中文件数",
    ]

    return any(keyword in message for keyword in summary_keywords)


class strm_downaload:
    def __init__(
        self,
        server: MediaServer,
        output_dir: str,
        enable_path_replacement: bool = False,
        replacement_path: str = "/nas",
        task_list: Optional[List] = None,
        max_threads: int = 20,
    ):
        self.server = server
        self.output_dir = output_dir
        self.base_url = server.base_url
        self.enable_path_replacement = enable_path_replacement
        self.replacement_path = replacement_path
        self.max_threads = max_threads
        # 下载列表
        self.task_list = task_list

    async def genstrm(self):
        """
        并发处理STRM任务

        Args:
            max_concurrent: 最大并发数，默认20
        """
        if not self.task_list:
            log.warning("没有任务需要处理")
            return

        total_tasks = len(self.task_list)

        # 分批处理，每批次最多处理max_concurrent个任务
        for i in range(0, total_tasks, self.max_threads):
            batch = self.task_list[i : i + self.max_threads]
            # 创建批处理任务
            batch_tasks = [self.process_single_task(task) for task in batch]

            # 并发执行
            await asyncio.gather(*batch_tasks, return_exceptions=True)

    async def process_single_task(self, task) -> bool:
        """
        处理单个STRM任务

        Args:
            task: 单个任务对象

        Returns:
            bool: 处理成功返回True，失败返回False
        """
        try:
            rest = await self.createstrm(task.source_path, self.server.base_url, self.output_dir)
            task.status = TaskStatus.COMPLETED
            task.target_path = rest
            await task.save()
            return True
        except Exception as e:
            error_msg = str(e)
            task.error_message = error_msg
            task.status = TaskStatus.FAILED
            await task.save()
            log.error(f"处理任务失败 [path={task.source_path}]: {error_msg}")
            return False

    async def createstrm(self, filepath, hosts, save_path):
        try:
            # 路径替换功能
            if self.enable_path_replacement:
                url = hosts + quote(self.replace_base_path(filepath, self.replacement_path))
            else:
                url = hosts + quote(filepath)
            root, ext = os.path.splitext(filepath)
            # 新后缀名（例如改为 .mp4）
            new_extension = ".strm"
            from pathlib import Path

            # 拼接新路径
            new_path = root + new_extension
            # 拼接保存路径
            save_path = save_path / Path(new_path).relative_to("/")
            # 使用通用函数确保目录存在
            directory = os.path.dirname(save_path)
            if directory:
                self.ensure_dir_exists(directory)
            async with aiofiles.open(save_path, "w") as f:
                await f.write(url)
            return save_path
        except Exception as e:
            log.error(f"未知错误: {str(e)}")
            log.error(f"❌ 📄｛save_path｝生成失败")
            raise  # 重新抛出异常给上层

    def replace_base_path(self, original_path, new_base_path):
        """
        替换路径中的主路径部分

        Args:
            original_path (str): 原始路径，如"/nas/动漫/一拳超人/S01E01.mkv"
            new_base_path (str): 新的主路径，nas2 "/nas2/动漫/一拳超人/S01E01.mkv"

        Returns:
            str: 替换主路径后的新路径
        """
        try:
            # 如果new_base_path头尾有/，则去掉
            if new_base_path.startswith("/"):
                new_base_path = new_base_path[1:]
            if new_base_path.endswith("/"):
                new_base_path = new_base_path[:-1]
            # 使用根对路径分割
            pathlist = original_path.split("/")
            # 替换第一层主路径
            pathlist[1] = new_base_path
            # 合并路径
            new_path = "/".join(pathlist)
            return new_path

        except Exception as e:
            log.error(f"❌ 替换路径错误: {str(e)}")
            return original_path

    def ensure_dir_exists(self, directory):
        try:
            if not os.path.exists(directory):
                os.makedirs(directory)
                # print("\n" + Fore.YELLOW + f"📁 创建目录: {directory}")

                return True
            return True
        except Exception as e:
            log.error(f"❌ 创建目录失败: {str(e)}")
            return False
