<template>
  <div class="console-log-viewer">
    <div ref="logContainer" class="log-container" :style="{ height: `${height}px` }" @scroll="handleScroll">
      <pre class="log-content"><code>
<span v-for="(line, index) in visibleLines" :key="index">
<span
  :class="getLineClass(line)"
  v-html="highlightSearchTerm(formatLine(line))"
></span>
</span>
      </code></pre>
    </div>
    <div class="controls">
      <n-switch v-model:value="autoScroll" size="small">
        <template #checked>自动滚动</template>
        <template #unchecked>自动滚动</template>
      </n-switch>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, PropType } from 'vue';
import { NSwitch } from 'naive-ui';

interface LogLine {
  text: string;
  level?: string;
}

const props = defineProps({
  logs: {
    type: Array as PropType<string[] | LogLine[]>,
    default: () => []
  },
  height: {
    type: Number,
    default: 500
  },
  searchTerm: {
    type: String,
    default: ''
  },
  levelFilter: {
    type: String,
    default: ''
  }
});

const logContainer = ref<HTMLElement | null>(null);
const autoScroll = ref(true);
const isUserScrolling = ref(false);
const visibleStartIndex = ref(0);
const itemsPerPage = ref(100); // 一次显示的行数

// 计算可见行
const filteredLogs = computed(() => {
  return props.logs.filter((line) => {
    // 如果是字符串类型
    if (typeof line === 'string') {
      // 级别过滤
      if (props.levelFilter && !line.includes(`[${props.levelFilter.toUpperCase()}]`)) {
        return false;
      }
      // 搜索词过滤
      if (props.searchTerm && !line.toLowerCase().includes(props.searchTerm.toLowerCase())) {
        return false;
      }
      return true;
    }
    // 如果是对象类型
    else {
      // 级别过滤
      if (props.levelFilter && line.level !== props.levelFilter.toUpperCase()) {
        return false;
      }
      // 搜索词过滤
      if (props.searchTerm && !line.text.toLowerCase().includes(props.searchTerm.toLowerCase())) {
        return false;
      }
      return true;
    }
  });
});

// 计算可见行的数量
const visibleLines = computed(() => {
  return filteredLogs.value.slice(
    visibleStartIndex.value,
    visibleStartIndex.value + itemsPerPage.value
  );
});

// 检测日志行级别并返回对应的CSS类
const getLineClass = (line: string | LogLine) => {
  let text: string;
  let level: string | undefined;

  if (typeof line === 'string') {
    text = line;
    // 从文本中提取日志级别
    const levelMatch = text.match(/\[(INFO|ERROR|WARNING|DEBUG)\]/i);
    level = levelMatch ? levelMatch[1].toUpperCase() : undefined;
  } else {
    text = line.text;
    level = line.level;
  }

  const classes = ['log-line'];

  if (level) {
    switch (level.toUpperCase()) {
      case 'ERROR':
        classes.push('log-error');
        break;
      case 'WARNING':
        classes.push('log-warning');
        break;
      case 'INFO':
        classes.push('log-info');
        break;
      case 'DEBUG':
        classes.push('log-debug');
        break;
    }
  }

  return classes.join(' ');
};

// 格式化日志行
const formatLine = (line: string | LogLine): string => {
  if (typeof line === 'string') {
    return line;
  } else {
    return line.text;
  }
};

// 高亮搜索词
const highlightSearchTerm = (text: string): string => {
  if (!props.searchTerm) return text;

  const regex = new RegExp(`(${escapeRegExp(props.searchTerm)})`, 'gi');
  return text.replace(regex, '<span class="highlight">$1</span>');
};

// 转义正则表达式特殊字符
const escapeRegExp = (string: string): string => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
};

// 处理滚动事件
const handleScroll = () => {
  if (!logContainer.value) return;

  const { scrollTop, scrollHeight, clientHeight } = logContainer.value;
  // 判断是否滚动到底部
  const isAtBottom = scrollHeight - scrollTop - clientHeight < 10;

  // 更新自动滚动状态
  if (isUserScrolling.value) {
    autoScroll.value = isAtBottom;
    isUserScrolling.value = false;
  }
};

// 滚动到底部
const scrollToBottom = () => {
  if (!logContainer.value) return;

  nextTick(() => {
    if (!logContainer.value) return;
    logContainer.value.scrollTop = logContainer.value.scrollHeight;
  });
};

// 监听日志变化，如果开启了自动滚动，则滚动到底部
watch(() => props.logs.length, (newVal, oldVal) => {
  if (newVal > oldVal && autoScroll.value) {
    scrollToBottom();
  }
});

// 监听过滤器变化，重置滚动位置
watch([() => props.searchTerm, () => props.levelFilter], () => {
  visibleStartIndex.value = 0;
  if (autoScroll.value) {
    scrollToBottom();
  }
});

// 组件挂载完成后滚动到底部
onMounted(() => {
  scrollToBottom();
});
</script>

<style scoped>
.console-log-viewer {
  display: flex;
  flex-direction: column;
  font-family: 'Consolas', 'Monaco', 'Courier New', Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
}

.log-container {
  background-color: #1e1e1e;
  color: #d4d4d4;
  overflow-y: auto;
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-all;
  padding: 4px;
}

.log-content {
  margin: 0;
  padding: 0;
}

.line-number {
  display: inline-block;
  min-width: 40px;
  padding-right: 8px;
  text-align: right;
  color: #858585;
  user-select: none;
}

.log-line {
  display: block;
  white-space: pre-wrap;
  word-break: break-all;
}

.log-error {
  color: #f14c4c;
}

.log-warning {
  color: #ddb100;
}

.log-info {
  color: #3794ff;
}

.log-debug {
  color: #888888;
}

.highlight {
  background-color: rgba(255, 215, 0, 0.3);
  color: white;
  border-radius: 2px;
}

.controls {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
}
</style>
