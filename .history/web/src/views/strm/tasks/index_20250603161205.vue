<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getStrmTasks } from '@/service/api';
import { useRoute } from 'vue-router';
import { useLoadingEmpty } from '@/hooks/common/loading';

const route = useRoute();
const { loading, startLoading, endLoading } = useLoadingEmpty();

// 任务列表
const tasks = ref<StrmAPI.StrmTaskResponse[]>([]);
// 当前任务ID
const currentTaskId = ref<number | null>(null);

// 获取任务列表
async function fetchTasks() {
  startLoading();
  try {
    const res = await getStrmTasks();
    tasks.value = res.data || [];

    // 如果URL中有任务ID，则选中该任务
    const taskId = route.query.id;
    if (taskId) {
      currentTaskId.value = Number(taskId);
    }
  } catch (error) {
    window.$message?.error('获取任务列表失败');
    console.error(error);
  } finally {
    endLoading();
  }
}

onMounted(() => {
  fetchTasks();
});
</script>

<template>
  <div class="h-full">
    <NCard title="任务管理" :bordered="false" class="h-full">
      <NSpin :show="loading">
        <template #description>加载中...</template>

        <div>
          <NAlert title="功能开发中" type="info" class="mb-16px">
            任务管理页面正在开发中，敬请期待...
          </NAlert>

          <NDataTable
            :columns="[
              { title: 'ID', key: 'id' },
              { title: '任务名称', key: 'name' },
              { title: '状态', key: 'status' },
              { title: '文件数', key: 'total_files' },
              { title: '创建时间', key: 'created_time' }
            ]"
            :data="tasks"
            :bordered="false"
            :single-line="false"
          />
        </div>
      </NSpin>
    </NCard>
  </div>
</template>
