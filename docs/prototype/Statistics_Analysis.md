# 115STRM管理平台统计分析界面原型

## 操作日志界面设计

![操作日志界面](https://raw.githubusercontent.com/yourusername/115strm-prototype/main/operation-logs.png)

### 布局结构
```
+----------------------------------------------------------+
|                     操作日志                              |
|                                                          |
|  [类型▼]  [时间范围▼]  [用户▼]  [级别▼]   [搜索框]        |
+----------------------------------------------------------+
|                                                          |
|                      日志列表                             |
|                                                          |
| +------------------------------------------------------+ |
| |  时间  |  用户  |  操作类型  |  内容摘要  |  级别  |    | |
| |--------|--------|-----------|-----------|--------|----|
| | 2023-05-20| admin | 文件上传   | 上传文件...| 信息   |[详情]|
| | 2023-05-20| user1 | STRM生成  | 生成STRM...| 信息   |[详情]|
| | 2023-05-19| admin | 系统设置   | 修改服务器...| 警告  |[详情]|
| | 2023-05-18| system| 系统       | 系统启动    | 信息   |[详情]|
| | 2023-05-18| user2 | STRM生成  | 生成失败...| 错误   |[详情]|
| | ...     | ...    | ...       | ...       | ...    | ... |
| +------------------------------------------------------+ |
|                                                          |
+----------------------------------------------------------+
| [导出日志] [清除过期日志]          显示1-10/共135条记录    |
+----------------------------------------------------------+
```

### 组件说明

#### 顶部操作栏
- 类型下拉过滤器 - 过滤不同类型的操作日志（全部、文件上传、STRM生成、服务器管理等）
- 时间范围选择器 - 选择日志的时间范围（今天、昨天、最近7天、最近30天、自定义）
- 用户下拉过滤器 - 按用户筛选日志
- 级别下拉过滤器 - 按日志级别筛选（全部、信息、警告、错误）
- 搜索框 - 按内容关键词搜索

#### 日志列表表格
- 列：
  - 时间（精确到秒）
  - 用户（操作执行者）
  - 操作类型（文件上传、STRM生成、服务器管理等）
  - 内容摘要（操作的简短描述）
  - 级别（信息、警告、错误）
  - 详情按钮
- 支持排序和过滤
- 不同级别的日志使用不同颜色标识
- 分页控件

#### 底部按钮
- 导出日志 - 将筛选后的日志导出到文件（CSV、Excel等）
- 清除过期日志 - 清除指定时间之前的日志（需确认）
- 分页信息和控件

### 交互说明
- 过滤器选择后自动刷新日志列表
- 点击详情按钮显示完整日志内容和相关上下文
- 日志列表支持按列排序
- 错误级别的日志默认高亮显示
- 支持批量操作（如批量导出）

## 日志详情界面设计

![日志详情界面](https://raw.githubusercontent.com/yourusername/115strm-prototype/main/log-detail.png)

### 布局结构
```
+----------------------------------------------------------+
|                     日志详情                              |
+----------------------------------------------------------+
|                                                          |
|  +------------------------------------------------------+|
|  | 基本信息                                              ||
|  |                                                      ||
|  | 日志ID: LOG-12345                                    ||
|  | 时间: 2023-05-20 15:30:45                            ||
|  | 用户: admin                                           ||
|  | IP地址: *************                                ||
|  | 操作类型: STRM生成                                    ||
|  | 级别: 错误                                            ||
|  +------------------------------------------------------+|
|                                                          |
|  +------------------------------------------------------+|
|  | 详细内容                                              ||
|  |                                                      ||
|  | 生成STRM文件失败。                                    ||
|  | 原因: 访问目标服务器(http://media-server.com)失败     ||
|  | 错误代码: ERR-5001                                   ||
|  | 影响文件: 10个                                        ||
|  |                                                      ||
|  +------------------------------------------------------+|
|                                                          |
|  +------------------------------------------------------+|
|  | 相关信息                                              ||
|  |                                                      ||
|  | 关联任务: Task-789                                    ||
|  | 服务器: MediaServer-01                                ||
|  | 源文件: /uploads/movies.txt                           ||
|  | 操作系统: Windows 10                                  ||
|  | 浏览器: Chrome 113.0.5672.93                          ||
|  |                                                      ||
|  +------------------------------------------------------+|
|                                                          |
|  +------------------------------------------------------+|
|  | 上下文日志                                            ||
|  |                                                      ||
|  | [15:30:40] [INFO] 开始生成STRM文件                    ||
|  | [15:30:42] [INFO] 处理文件1.mp4                       ||
|  | [15:30:45] [ERROR] 访问服务器失败                     ||
|  | [15:30:46] [INFO] 任务中止                            ||
|  |                                                      ||
|  +------------------------------------------------------+|
|                                                          |
|  [返回日志列表] [查看相关任务] [导出此日志]                |
+----------------------------------------------------------+
```

### 组件说明

#### 基本信息卡片
- 日志ID
- 记录时间（精确到秒）
- 操作用户
- IP地址
- 操作类型
- 日志级别（带颜色标识）

#### 详细内容卡片
- 完整的日志内容
- 错误原因（如适用）
- 错误代码（如适用）
- 影响范围
- 其他详细说明

#### 相关信息卡片
- 关联任务或操作
- 涉及的服务器
- 涉及的文件
- 环境信息（操作系统、浏览器等）
- 其他上下文信息

#### 上下文日志卡片
- 显示当前日志前后的相关日志
- 时间顺序排列
- 突出显示当前日志

#### 底部按钮
- 返回日志列表 - 返回操作日志页面
- 查看相关任务 - 跳转到关联的任务详情页（如适用）
- 导出此日志 - 将当前日志及上下文导出到文件

### 交互说明
- 根据日志类型显示不同的相关信息字段
- 上下文日志可以展开查看更多
- 错误日志提供可能的解决方案或参考链接
- 可以直接从日志跳转到相关页面（如任务详情）

## 数据统计界面设计

![数据统计界面](https://raw.githubusercontent.com/yourusername/115strm-prototype/main/statistics.png)

### 布局结构
```
+----------------------------------------------------------+
|                     数据统计                              |
|                                                          |
|  [时间范围▼]  [统计类型▼]  [导出] [刷新]                  |
+----------------------------------------------------------+
| +----------------------+ +-----------------------------+ |
| |                      | |                             | |
| |     处理文件统计      | |      媒体类型分布           | |
| |    (折线图/柱状图)    | |        (饼图)              | |
| |                      | |                             | |
| +----------------------+ +-----------------------------+ |
+----------------------------------------------------------+
| +----------------------+ +-----------------------------+ |
| |                      | |                             | |
| |     用户活动统计      | |      服务器使用情况         | |
| |      (柱状图)        | |       (折线图)             | |
| |                      | |                             | |
| +----------------------+ +-----------------------------+ |
+----------------------------------------------------------+
|                                                          |
|                      数据明细                             |
|                                                          |
| +------------------------------------------------------+ |
| |  日期  |  处理文件  |  成功  |  失败  |  用户数  | 大小 | |
| |--------|-----------|--------|--------|----------|------| |
| | 2023-05-20| 1,250     | 1,200  | 50     | 5        | 2.5GB| |
| | 2023-05-19| 980       | 950    | 30     | 4        | 1.8GB| |
| | 2023-05-18| 1,500     | 1,450  | 50     | 6        | 3.2GB| |
| | ...     | ...       | ...    | ...    | ...      | ...  | |
| +------------------------------------------------------+ |
|                                                          |
+----------------------------------------------------------+
```

### 组件说明

#### 顶部操作栏
- 时间范围选择器 - 选择统计数据的时间范围（今天、昨天、最近7天、最近30天、自定义）
- 统计类型下拉选择 - 选择不同的统计视图（概览、文件处理、用户活动等）
- 导出按钮 - 导出统计数据
- 刷新按钮 - 刷新统计数据

#### 处理文件统计卡片
- 折线图或柱状图，显示:
  - X轴: 日期
  - Y轴: 处理文件数量
  - 可区分成功和失败
  - 支持缩放和鼠标悬停查看详情

#### 媒体类型分布卡片
- 饼图，显示不同类型媒体文件的分布:
  - 电影
  - 电视剧
  - 音乐
  - 其他
- 点击饼图切片可查看该类型的详情

#### 用户活动统计卡片
- 柱状图，显示:
  - X轴: 用户
  - Y轴: 操作数量
  - 可区分不同类型的操作
  - 支持排序和筛选

#### 服务器使用情况卡片
- 折线图，显示:
  - X轴: 时间
  - Y轴: 请求数/流量
  - 可显示多个服务器的数据
  - 支持缩放和对比

#### 数据明细表格
- 按日期汇总的详细数据
- 列:
  - 日期
  - 处理文件数
  - 成功数
  - 失败数
  - 活跃用户数
  - 总数据大小
- 支持排序和导出

### 交互说明
- 时间范围选择后自动刷新所有图表
- 图表支持交互操作（缩放、筛选、悬停查看详情）
- 点击图表元素可查看相关明细数据
- 支持数据钻取，从概览到详情
- 所有图表可以单独放大查看

## 数据报表界面设计

![数据报表界面](https://raw.githubusercontent.com/yourusername/115strm-prototype/main/reports.png)

### 布局结构
```
+----------------------------------------------------------+
|                     数据报表                              |
|                                                          |
|  [报表类型▼]  [时间范围▼]  [生成报表]                     |
+----------------------------------------------------------+
|                                                          |
|  +------------------------------------------------------+|
|  | 报表参数                                              ||
|  |                                                      ||
|  | 包含内容:                                            ||
|  | [✓] 文件处理统计  [✓] 用户活动  [✓] 媒体类型分布      ||
|  | [✓] 服务器使用情况  [ ] 错误分析  [ ] 系统性能        ||
|  |                                                      ||
|  | 报表格式: (●) PDF  ( ) Excel  ( ) CSV                ||
|  |                                                      ||
|  | 自动发送: [ ] 启用                                    ||
|  | 接收邮箱: [                                ]          ||
|  | 发送频率: [  下拉选择                      ]          ||
|  |                                                      ||
|  +------------------------------------------------------+|
|                                                          |
|                      已生成报表                           |
|                                                          |
| +------------------------------------------------------+ |
| |  报表名称  |  生成时间  |  类型  |  大小  |    操作    | |
| |------------|-----------|--------|--------|------------| |
| | 周报告     | 2023-05-20| 文件处理| 1.2MB  | [查看][下载]| |
| | 月度统计   | 2023-05-01| 全部    | 3.5MB  | [查看][下载]| |
| | 错误分析   | 2023-04-15| 错误分析| 0.8MB  | [查看][下载]| |
| | ...        | ...       | ...    | ...    | ...         | |
| +------------------------------------------------------+ |
|                                                          |
+----------------------------------------------------------+
| [定时报表设置] [模板管理]                                 |
+----------------------------------------------------------+
```

### 组件说明

#### 顶部操作栏
- 报表类型下拉选择 - 选择不同类型的预设报表（概览报表、文件处理报表、用户活动报表等）
- 时间范围选择器 - 选择报表数据的时间范围
- 生成报表按钮 - 根据当前设置生成新报表

#### 报表参数卡片
- 包含内容复选框组 - 选择报表中包含的数据部分
- 报表格式单选按钮组 - 选择输出格式（PDF、Excel、CSV等）
- 自动发送设置:
  - 启用复选框
  - 接收邮箱输入框
  - 发送频率下拉选择（每日、每周、每月等）

#### 已生成报表表格
- 列:
  - 报表名称
  - 生成时间
  - 报表类型
  - 文件大小
  - 操作按钮（查看、下载、删除）
- 支持排序和筛选
- 分页控件

#### 底部按钮
- 定时报表设置 - 打开定时报表配置页面
- 模板管理 - 管理报表模板

### 交互说明
- 报表类型选择会自动调整包含内容的默认选择
- 生成报表后自动显示在已生成报表列表中
- 查看按钮打开报表预览
- 支持报表的在线查看和下载
- 定时报表设置允许配置自动生成和发送报表的计划 