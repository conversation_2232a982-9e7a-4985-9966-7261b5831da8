<script setup lang="ts">
import { computed, h, onMounted, ref } from 'vue';
import { useMessage, useNotification } from 'naive-ui';
import { getMediaServers, getSystemSettings, updateSystemSettings } from '@/service/api/strm';
import { $t } from '@/locales';
import ServerManagement from '@/components/custom/server-management.vue';

defineOptions({ name: 'ManageSystemSettings' });

const message = useMessage();
const notification = useNotification();

// 页面状态
const loading = ref(false);
const submitting = ref(false);
const activeTab = ref('basicSettings'); // 当前激活的选项卡: basicSettings 或 serverManagement

// 数据模型
const settings = ref({
  enablePathReplacement: false,
  replacementPath: '',
  downloadThreads: 0,
  outputDirectory: '',
  defaultMediaServerId: null as number | null,
  defaultDownloadServerId: null as number | null,
  videoFileTypes: '',
  audioFileTypes: '',
  imageFileTypes: '',
  subtitleFileTypes: '',
  metadataFileTypes: ''
});

// 服务器列表
const mediaServers = ref<Array<{ label: string; value: number }>>([]);
const downloadServers = ref<Array<{ label: string; value: number }>>([]);
// 所有服务器的原始数据，用于传递给服务器管理组件
const allServers = ref<Array<any>>([]);
// 标记服务器列表是否已加载
const serversLoaded = ref(false);

// 文件类型标签数据
const videoFileTypeTags = computed({
  get: () => settings.value.videoFileTypes.split(',').filter(item => item.trim()),
  set: (val) => { settings.value.videoFileTypes = val.join(','); }
});

const audioFileTypeTags = computed({
  get: () => settings.value.audioFileTypes.split(',').filter(item => item.trim()),
  set: (val) => { settings.value.audioFileTypes = val.join(','); }
});

const imageFileTypeTags = computed({
  get: () => settings.value.imageFileTypes.split(',').filter(item => item.trim()),
  set: (val) => { settings.value.imageFileTypes = val.join(','); }
});

const subtitleFileTypeTags = computed({
  get: () => settings.value.subtitleFileTypes.split(',').filter(item => item.trim()),
  set: (val) => { settings.value.subtitleFileTypes = val.join(','); }
});

const metadataFileTypeTags = computed({
  get: () => settings.value.metadataFileTypes.split(',').filter(item => item.trim()),
  set: (val) => { settings.value.metadataFileTypes = val.join(','); }
});

// 格式化文件扩展名（确保以.开头）
function formatExtension(ext: string): string {
  const trimmed = ext.trim();
  if (!trimmed.startsWith('.') && trimmed !== '') {
    return `.${trimmed}`;
  }
  return trimmed;
}

// 验证文件扩展名是否有效
function validateExtension(ext: string): boolean {
  // 允许空字符串
  if (!ext) return true;
  // 扩展名应该以点开头，后面跟1-10个字母或数字
  const regex = /^\.[a-zA-Z0-9]{1,10}$/;
  return regex.test(ext);
}

// 标签添加前的验证
function beforeAddTag(tag: string, tagType: string): boolean | string {
  const formatted = formatExtension(tag);
  console.log(`验证扩展名: ${formatted}, 类型: ${tagType}`);

  // 验证格式
  if (!validateExtension(formatted)) {
    message.warning('无效的文件扩展名格式');
    return false;
  }

  // 检查当前类型中是否有重复
  const existingTags = getTagsRefByType(tagType).value;
  const hasDuplicate = existingTags.some(t => t.toLowerCase() === formatted.toLowerCase());
  if (hasDuplicate) {
    message.warning('该扩展名已存在');
    console.log(`扩展名 ${formatted} 在当前类型 ${tagType} 中已存在`);
    return false;
  }

  // 不再检查其他类型中是否有重复，由后端进行验证

  console.log(`扩展名 ${formatted} 验证通过`);
  return formatted;
}

// 根据类型获取对应的标签引用
function getTagsRefByType(type: string): typeof videoFileTypeTags {
  switch (type) {
    case 'video': return videoFileTypeTags;
    case 'audio': return audioFileTypeTags;
    case 'image': return imageFileTypeTags;
    case 'subtitle': return subtitleFileTypeTags;
    case 'metadata': return metadataFileTypeTags;
    default: return videoFileTypeTags;
  }
}

// 获取不同文件类型的标签样式
function getTagType(tagType: string): 'success' | 'info' | 'warning' | 'error' | 'default' | 'primary' {
  switch (tagType) {
    case 'video': return 'success';
    case 'audio': return 'info';
    case 'image': return 'warning';
    case 'subtitle': return 'error';
    case 'metadata': return 'primary';
    default: return 'default';
  }
}

// 获取系统设置
async function fetchSettings() {
  try {
    loading.value = true;
    const res = await getSystemSettings();
    if (res.data) {
      // 设置系统参数
      settings.value = {
        enablePathReplacement: res.data.enable_path_replacement ?? false,
        replacementPath: res.data.replacement_path ?? '',
        downloadThreads: res.data.download_threads ?? 0,
        outputDirectory: res.data.output_directory ?? '',
        defaultMediaServerId: res.data.default_media_server_id ?? null,
        defaultDownloadServerId: res.data.default_download_server_id ?? null,
        videoFileTypes: res.data.video_file_types ?? '',
        audioFileTypes: res.data.audio_file_types ?? '',
        imageFileTypes: res.data.image_file_types ?? '',
        subtitleFileTypes: res.data.subtitle_file_types ?? '',
        metadataFileTypes: res.data.metadata_file_types ?? ''
      };

      // 如果需要显示默认服务器设置，则需要加载服务器列表
      if (settings.value.defaultMediaServerId !== null || settings.value.defaultDownloadServerId !== null) {
        await loadServersIfNeeded();
      }
    }
  } catch (error: any) {
    message.error(error.message || $t('strm.settings.saveFail'));
  } finally {
    loading.value = false;
  }
}

// 获取服务器列表（仅在需要时调用）
async function fetchServers() {
  console.log('开始获取服务器列表，当前加载状态:', serversLoaded.value);
  if (serversLoaded.value) {
    console.log('服务器列表已加载，跳过请求');
    return; // 如果已加载过，则不重复加载
  }

  try {
    // 获取所有服务器
    console.log('发送获取服务器列表请求');
    const allRes = await getMediaServers();
    console.log('获取服务器列表响应:', allRes);

    if (allRes.data && Array.isArray(allRes.data)) {
      // 保存原始服务器数据
      allServers.value = allRes.data;
      console.log('成功加载服务器列表', allServers.value);

      // 过滤出媒体服务器（xiaoyahost类型）
      mediaServers.value = allRes.data
        .filter(server => server.server_type === 'xiaoyahost')
        .map(server => ({
          label: server.name,
          value: server.id
        }));

      // 过滤出下载服务器（cd2host类型）
      downloadServers.value = allRes.data
        .filter(server => server.server_type === 'cd2host')
        .map(server => ({
          label: server.name,
          value: server.id
        }));

      serversLoaded.value = true; // 标记为已加载
    } else {
      console.error('服务器列表数据格式无效:', allRes);
    }
  } catch (error: any) {
    console.error('获取服务器列表失败:', error);
    message.error(error.message || $t('strm.settings.saveFail'));
  }
}

// 仅在需要时加载服务器列表
async function loadServersIfNeeded() {
  if (!serversLoaded.value) {
    await fetchServers();
  }
}

// 保存设置
async function saveSettings() {
  // 添加标志变量，跟踪是否已显示错误
  let errorShown = false;

  try {
    submitting.value = true;

    const requestData = {
      enable_path_replacement: settings.value.enablePathReplacement,
      replacement_path: settings.value.replacementPath,
      download_threads: settings.value.downloadThreads,
      output_directory: settings.value.outputDirectory,
      default_media_server_id: settings.value.defaultMediaServerId,
      default_download_server_id: settings.value.defaultDownloadServerId,
      video_file_types: settings.value.videoFileTypes,
      audio_file_types: settings.value.audioFileTypes,
      image_file_types: settings.value.imageFileTypes,
      subtitle_file_types: settings.value.subtitleFileTypes,
      metadata_file_types: settings.value.metadataFileTypes
    };

    console.log('保存系统设置，请求数据：', requestData);

    // 使用统一的API函数发送请求
    const response = await updateSystemSettings(requestData);

    console.log('保存系统设置响应：', response);

    // 增强的错误检测逻辑
    if (
      (response && response.code && response.code !== "0000") || // 标准错误码
      (response && response.error) // error字段
    ) {
      console.log('检测到错误响应');

      // 提取错误消息
      let errorMsg = '';
      if (response.msg) {
        errorMsg = response.msg;
      } else if (response.error && response.error.response && response.error.response.data) {
        errorMsg = response.error.response.data.msg || response.error.message;
        console.log('提取的错误消息:', errorMsg);
      }

      // 显示错误通知
      if (errorMsg && (errorMsg.includes('文件类型设置有误') || errorMsg.includes('扩展名'))) {
        notification.error({
          title: '文件类型设置错误',
          content: () => {
            const errorLines = errorMsg.split('\n');
            return h('div', {}, errorLines.map((line: string) => h('p', {}, line)));
          },
          duration: 10000
        });
        // 标记已显示错误
        errorShown = true;
      } else {
        message.error(errorMsg || $t('strm.settings.saveFail'));
        // 标记已显示错误
        errorShown = true;
      }

      // 如果检测到错误，不显示成功消息
      return;
    }

    // 如果没有检测到错误，显示成功消息
    message.success($t('strm.settings.saveSuccess'));

    // 刷新设置
    await fetchSettings();
  } catch (error: any) {
    console.error('保存系统设置出错：', error);

    // 检查是否已显示错误通知，如果已显示则不再显示
    if (!errorShown) {
      // 检查错误对象中是否包含文件类型重复的错误信息
      const errorMsg = error.response?.data?.msg || error.message || '';

      if (errorMsg && (errorMsg.includes('文件类型设置有误') || errorMsg.includes('扩展名'))) {
        // 使用notification组件显示详细的错误信息
        notification.error({
          title: '文件类型设置错误',
          content: () => {
            // 分行显示错误信息
            const errorLines = errorMsg.split('\n');
            return h('div', {}, errorLines.map((line: string) => h('p', {}, line)));
          },
          duration: 10000 // 显示10秒
        });
      } else {
        // 其他错误使用常规消息提示
        message.error(errorMsg || $t('strm.settings.saveFail'));
      }
    }
  } finally {
    submitting.value = false;
  }
}

// 切换标签页
function handleTabChange(name: string) {
  console.log('切换标签页到:', name);
  activeTab.value = name;
  // 如果切换到服务器管理选项卡，则直接加载服务器列表
  if (name === 'serverManagement') {
    console.log('切换到服务器管理标签页，直接加载服务器列表');
    // 不再使用loadServersIfNeeded，而是直接调用fetchServers
    fetchServers();
  }
}

// 服务器变更时刷新列表
function handleServersUpdate() {
  // 重置标记，重新加载服务器列表
  serversLoaded.value = false;
  fetchServers();
}

// 初始化
onMounted(async () => {
  console.log('组件初始化，当前活动标签:', activeTab.value);
  await fetchSettings();
  console.log('设置数据加载完成，当前活动标签:', activeTab.value);

  // 如果当前选项卡是服务器管理，则加载服务器列表
  if (activeTab.value === 'serverManagement') {
    console.log('初始化时发现当前是服务器管理标签，加载服务器列表');
    await fetchServers();
  }
});
</script>

<template>
  <n-card :title="$t('strm.settings.title')" :bordered="false">
    <n-tabs v-model:value="activeTab" type="line" animated @update:value="handleTabChange">
      <!-- 基本设置选项卡 -->
      <n-tab-pane name="basicSettings" :tab="$t('strm.settings.basicSettings')">
        <n-spin :show="loading">
          <n-form label-placement="left" label-width="auto" require-mark-placement="right-hanging"
            :disabled="submitting" class="pt-20px">

            <!-- 默认下载服务器 -->
            <n-form-item :label="$t('strm.settings.defaultDownloadServer')">
              <n-select v-model:value="settings.defaultDownloadServerId" :options="downloadServers"
                :placeholder="$t('strm.settings.defaultDownloadServerPlaceholder')" clearable
                @focus="loadServersIfNeeded" />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.defaultDownloadServerHelp')
                }}</span>
              </template>
            </n-form-item>

            <!-- 默认媒体服务器 -->
            <n-form-item :label="$t('strm.settings.defaultMediaServer')">
              <n-select v-model:value="settings.defaultMediaServerId" :options="mediaServers"
                :placeholder="$t('strm.settings.defaultMediaServerPlaceholder')" clearable
                @focus="loadServersIfNeeded" />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.defaultMediaServerHelp')
                }}</span>
              </template>
            </n-form-item>

            <!-- 路径替换开关 -->
            <n-form-item :label="$t('strm.settings.enablePathReplacement')">
              <n-switch v-model:value="settings.enablePathReplacement" />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.enablePathReplacementHelp')
                }}</span>
              </template>
            </n-form-item>

            <!-- 路径替换值，只在开启时显示 -->
            <n-form-item :label="$t('strm.settings.replacementPath')" v-show="settings.enablePathReplacement">
              <n-input v-model:value="settings.replacementPath"
                :placeholder="$t('strm.settings.replacementPathPlaceholder')" />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.replacementPathHelp') }}</span>
              </template>
            </n-form-item>

            <!-- 下载线程数 -->
            <n-form-item :label="$t('strm.settings.downloadThreads')">
              <n-input-number v-model:value="settings.downloadThreads" :min="1" :max="20" />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.downloadThreadsHelp')
                }}</span>
              </template>
            </n-form-item>

            <!-- 输出目录 -->
            <n-form-item :label="$t('strm.settings.outputDirectory')">
              <n-input v-model:value="settings.outputDirectory"
                :placeholder="$t('strm.settings.outputDirectoryPlaceholder')" />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.outputDirectoryHelp')
                }}</span>
              </template>
            </n-form-item>

            <!-- 文件类型分隔线 -->
            <n-divider>{{ $t('strm.settings.fileTypesSettings') }}</n-divider>

            <!-- 视频文件类型 -->
            <n-form-item :label="$t('strm.settings.videoFileTypes')">
              <n-dynamic-tags v-model:value="videoFileTypeTags" :type="getTagType('video')" :input-props="{
                placeholder: $t('strm.settings.addExtensionPlaceholder')
              }" :add-button-props="{
                dashed: true,
                round: true
              }" :max="20" :before-add-tag="(tag: string) => beforeAddTag(tag, 'video')" />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.fileTypesHelp') }}</span>
              </template>
            </n-form-item>

            <!-- 音频文件类型 -->
            <n-form-item :label="$t('strm.settings.audioFileTypes')">
              <n-dynamic-tags v-model:value="audioFileTypeTags" :type="getTagType('audio')" :input-props="{
                placeholder: $t('strm.settings.addExtensionPlaceholder')
              }" :add-button-props="{
                dashed: true,
                round: true
              }" :max="20" :before-add-tag="(tag: string) => beforeAddTag(tag, 'audio')" />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.fileTypesHelp') }}</span>
              </template>
            </n-form-item>

            <!-- 图片文件类型 -->
            <n-form-item :label="$t('strm.settings.imageFileTypes')">
              <n-dynamic-tags v-model:value="imageFileTypeTags" :type="getTagType('image')" :input-props="{
                placeholder: $t('strm.settings.addExtensionPlaceholder')
              }" :add-button-props="{
                dashed: true,
                round: true
              }" :max="20" :before-add-tag="(tag: string) => beforeAddTag(tag, 'image')" />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.fileTypesHelp') }}</span>
              </template>
            </n-form-item>

            <!-- 字幕文件类型 -->
            <n-form-item :label="$t('strm.settings.subtitleFileTypes')">
              <n-dynamic-tags v-model:value="subtitleFileTypeTags" :type="getTagType('subtitle')" :input-props="{
                placeholder: $t('strm.settings.addExtensionPlaceholder')
              }" :add-button-props="{
                dashed: true,
                round: true
              }" :max="20" :before-add-tag="(tag: string) => beforeAddTag(tag, 'subtitle')" />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.fileTypesHelp') }}</span>
              </template>
            </n-form-item>

            <!-- 元数据文件类型 -->
            <n-form-item :label="$t('strm.settings.metadataFileTypes')">
              <n-dynamic-tags v-model:value="metadataFileTypeTags" :type="getTagType('metadata')" :input-props="{
                placeholder: $t('strm.settings.addExtensionPlaceholder')
              }" :add-button-props="{
                dashed: true,
                round: true
              }" :max="20" :before-add-tag="(tag: string) => beforeAddTag(tag, 'metadata')" />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.fileTypesHelp') }}</span>
              </template>
            </n-form-item>

            <!-- 保存按钮 -->
            <n-form-item>
              <n-button type="primary" :loading="submitting" @click="saveSettings">{{
                $t('strm.settings.save') }}</n-button>
            </n-form-item>
          </n-form>
        </n-spin>
      </n-tab-pane>

      <!-- 服务器管理选项卡 -->
      <n-tab-pane name="serverManagement" :tab="$t('strm.settings.serverManagement')">
        <server-management :auto-load="true" :external-servers="allServers" @update:servers="handleServersUpdate" />
      </n-tab-pane>
    </n-tabs>
  </n-card>
</template>

<style scoped>
.pt-20px {
  padding-top: 20px;
}
</style>
