"""
STRM管理模块API

注意：异常处理最佳实践
-------------------------------
本项目推荐使用自定义的HTTPException类而不是FastAPI的HTTPException类。

推荐用法:
```python
from app.core.exceptions import HTTPException
raise HTTPException(code="4001", msg="认证失败")
```

而不是:
```python
from fastapi import HTTPException
raise HTTPException(status_code=401, detail="认证失败")
```

系统已添加兼容层处理两种类型的异常，但为保持一致性，请尽量使用自定义HTTPException。
"""

from fastapi import (
    APIRouter,
    Depends,
    UploadFile,
    File,
    Body,
    Query,
    Path,
    Request,
    WebSocket,
    WebSocketDisconnect,
    status,
)
from sse_starlette.sse import EventSourceResponse
import json
from app.controllers.strm.upload import (
    handle_file_upload,
    parse_uploaded_file,
    delete_upload_record,
    download_upload_file,
    get_parse_result,
    handle_url_upload,
    get_directory_content,
    search_files,
)
from app.controllers.strm.task_controller import (
    create_strm_task,
    start_strm_task,
    get_task_status,
    get_user_tasks,
    download_strm_files,
    cancel_task,
    delete_task,
    get_task_logs,
    task_status_event_generator,
)
from app.core.dependency import get_current_user, check_token, AuthControl
from app.models.system import User
from app.schemas.base import Success, SuccessExtra
from app.schemas.strm.schemas import (
    ParseRequest,
    UploadHistoryResponse,
    UrlUploadRequest,
    MediaServerResponse,
    StrmTaskCreate,
    StrmTaskResponse,
    SystemSettingsUpdate,
    SystemSettingsResponse,
    MediaServerBase,
)
from app.models.strm.upload import UploadRecord
from app.models.strm import MediaServer
from typing import List, Optional
from fastapi.responses import FileResponse, JSONResponse
from app.core.exceptions import HTTPException
from app.controllers.strm import system_settings_controller
from app.controllers.strm.server_controller import server_controller
import asyncio
from datetime import datetime

router_strm = APIRouter()


# 新增：WebSocket连接管理类
class ConnectionManager:
    def __init__(self):
        # 存储活动的WebSocket连接，格式为 {task_id: {user_id: websocket}}
        self.active_connections = {}

    async def connect(self, websocket: WebSocket, task_id: int, user_id: int):
        """添加新的WebSocket连接"""
        await websocket.accept()

        # 确保任务ID有对应的字典
        if task_id not in self.active_connections:
            self.active_connections[task_id] = {}

        # 存储websocket连接
        self.active_connections[task_id][user_id] = websocket

    def disconnect(self, task_id: int, user_id: int):
        """移除WebSocket连接"""
        if task_id in self.active_connections:
            if user_id in self.active_connections[task_id]:
                del self.active_connections[task_id][user_id]

            # 如果任务没有更多连接，则删除该任务项
            if not self.active_connections[task_id]:
                del self.active_connections[task_id]

    async def send_json(self, task_id: int, user_id: int, data: dict, event_type: str = None):
        """向指定用户的WebSocket连接发送JSON数据"""
        from app.log.log import log

        if task_id in self.active_connections and user_id in self.active_connections[task_id]:
            websocket = self.active_connections[task_id][user_id]
            message = {"data": data}
            if event_type:
                message["event"] = event_type

            log.debug(f"准备发送WebSocket消息: 任务={task_id}, 用户={user_id}, 事件类型={event_type}")

            try:
                # 检查WebSocket连接状态
                if websocket.client_state == WebSocket.CONNECTED:
                    await websocket.send_json(message)
                    log.debug(f"WebSocket消息发送成功: 任务={task_id}, 用户={user_id}, 事件类型={event_type}")
                    return True
                else:
                    log.warning(
                        f"WebSocket连接已关闭，无法发送消息: 任务={task_id}, 用户={user_id}, 连接状态={websocket.client_state}"
                    )
                    # 移除断开的连接
                    self.disconnect(task_id, user_id)
                    return False
            except Exception as e:
                import traceback

                log.error(f"WebSocket发送数据失败: 任务={task_id}, 用户={user_id}, 错误={str(e)}")
                log.debug(f"WebSocket发送错误详情: {traceback.format_exc()}")
                # 发生错误时移除连接
                self.disconnect(task_id, user_id)
                return False
        else:
            log.warning(f"找不到WebSocket连接: 任务={task_id}, 用户={user_id}")
            return False

    async def broadcast(self, task_id: int, data: dict, event_type: str = None):
        """向所有订阅特定任务的WebSocket连接广播数据"""
        if task_id in self.active_connections:
            for user_id, websocket in list(self.active_connections[task_id].items()):
                try:
                    message = {"data": data}
                    if event_type:
                        message["event"] = event_type

                    await websocket.send_json(message)
                except Exception as e:
                    from app.log.log import log

                    log.error(f"WebSocket广播数据失败 (用户 {user_id}): {str(e)}")
                    # 出错时删除该连接
                    self.disconnect(task_id, user_id)


# 创建连接管理器实例
manager = ConnectionManager()


async def authenticate_websocket(websocket: WebSocket) -> Optional[User]:
    """验证WebSocket连接的token，支持两种方式：
    1. 从请求头获取Bearer token (标准方式)
    2. 从URL查询参数中获取token参数 (替代方式)
    """
    from app.log.log import log

    current_user = None

    # 方法1: 从请求头获取token
    authorization = websocket.headers.get("authorization")
    if authorization and authorization.startswith("Bearer "):
        token = authorization.replace("Bearer ", "")
        if token:
            log.debug(f"WebSocket认证: 从请求头获取到Bearer token (前5位): {token[:5]}...")
            try:
                # 验证token
                status, code, decode_data = check_token(token)
                if status:
                    if decode_data["data"]["tokenType"] == "accessToken":
                        user_id = decode_data["data"]["userId"]
                        current_user = await User.filter(id=user_id).first()
                        if current_user:
                            log.info(
                                f"WebSocket认证: 通过请求头成功认证用户 {current_user.user_name} (ID: {current_user.id})"
                            )
                        else:
                            log.warning(f"WebSocket认证: 找不到ID为 {user_id} 的用户")
                    else:
                        log.warning(f"WebSocket认证: token类型不是accessToken: {decode_data['data'].get('tokenType')}")
                else:
                    log.warning(f"WebSocket认证: token验证失败, 错误码: {code}")
            except Exception as e:
                import traceback

                log.error(f"WebSocket认证: 处理请求头token时发生异常: {str(e)}\n{traceback.format_exc()}")
        else:
            log.warning("WebSocket认证: 提供了空token")
    else:
        log.debug("WebSocket认证: 请求头中没有Bearer token，尝试从URL查询参数获取")

    # 方法2: 从URL查询参数获取token
    if current_user is None:
        try:
            # 从查询参数获取token
            token = None
            for param, value in websocket.query_params.items():
                if param.lower() == "token":
                    token = value
                    break

            if token:
                # 如果token被引号包裹（例如JSON序列化），去除引号
                if token.startswith('"') and token.endswith('"'):
                    token = token[1:-1]
                if token.startswith("'") and token.endswith("'"):
                    token = token[1:-1]

                log.debug(f"WebSocket认证: 从URL查询参数获取到token (前5位): {token[:5]}...")
                # 验证token
                status, code, decode_data = check_token(token)
                if status:
                    if decode_data["data"]["tokenType"] == "accessToken":
                        user_id = decode_data["data"]["userId"]
                        current_user = await User.filter(id=user_id).first()
                        if current_user:
                            log.info(
                                f"WebSocket认证: 通过URL查询参数成功认证用户 {current_user.user_name} (ID: {current_user.id})"
                            )
                        else:
                            log.warning(f"WebSocket认证: 找不到ID为 {user_id} 的用户")
                    else:
                        log.warning(f"WebSocket认证: token类型不是accessToken: {decode_data['data'].get('tokenType')}")
                else:
                    log.warning(f"WebSocket认证: URL参数token验证失败, 错误码: {code}")
            else:
                log.warning("WebSocket认证: URL查询参数中没有token")
        except Exception as e:
            import traceback

            log.error(f"WebSocket认证: 处理URL参数token时发生异常: {str(e)}\n{traceback.format_exc()}")

    # 如果没有通过任何方式获取到current_user，则认证失败
    if current_user is None:
        log.warning("WebSocket认证: 未能通过请求头或URL查询参数验证用户身份")
        return None

    return current_user


async def authenticate_user_from_request(request: Request, token: Optional[str] = None):
    """从请求中获取用户信息，支持token或cookie认证"""
    from app.log.log import log

    current_user = None
    log.debug(f"SSE认证: 开始验证用户身份, URL路径: {request.url.path}")

    # 记录token情况
    if token == "":
        log.warning("SSE认证: 提供了空token参数")
    elif token:
        log.debug(f"SSE认证: 收到token参数 (前5位): {token[:5]}...")
    else:
        log.debug("SSE认证: 未提供token参数")

    # 方法1: 从请求头获取token
    auth_header = request.headers.get("Authorization")
    if auth_header and auth_header.startswith("Bearer "):
        bearer_token = auth_header.replace("Bearer ", "")
        if bearer_token:
            log.debug(f"SSE认证: 从请求头获取到Bearer token (前5位): {bearer_token[:5]}...")
            status, code, decode_data = check_token(bearer_token)
            if status:
                if decode_data["data"]["tokenType"] == "accessToken":
                    user_id = decode_data["data"]["userId"]
                    current_user = await User.filter(id=user_id).first()
                    if current_user:
                        log.info(
                            f"SSE认证: 通过Authorization头成功认证用户 {current_user.user_name} (ID: {current_user.id})"
                        )
                    else:
                        log.warning(f"SSE认证: 找不到ID为 {user_id} 的用户")
                else:
                    log.warning(f"SSE认证: token类型不是accessToken: {decode_data['data'].get('tokenType')}")
            else:
                log.warning(f"SSE认证: Bearer token验证失败, 错误码: {code}, 原因: {decode_data}")
        else:
            log.warning("SSE认证: Authorization头以Bearer开头但没有实际token")

    # 方法2: 从查询参数获取token
    if current_user is None and token:
        # 防止尝试解析空token
        if len(token.strip()) == 0:
            log.warning("SSE认证: 查询参数中的token为空，跳过验证")
        else:
            log.debug(f"SSE认证: 尝试验证查询参数中的token (前5位): {token[:5]}...")
            try:
                status, code, decode_data = check_token(token)
                if status:
                    if decode_data["data"]["tokenType"] == "accessToken":
                        user_id = decode_data["data"]["userId"]
                        current_user = await User.filter(id=user_id).first()
                        if current_user:
                            log.info(
                                f"SSE认证: 通过查询参数token成功认证用户 {current_user.user_name} (ID: {current_user.id})"
                            )
                        else:
                            log.warning(f"SSE认证: 找不到ID为 {user_id} 的用户")
                    else:
                        log.warning(f"SSE认证: token类型不是accessToken: {decode_data['data'].get('tokenType')}")
                else:
                    log.warning(f"SSE认证: 查询参数token验证失败, 错误码: {code}, 原因: {decode_data}")
            except Exception as e:
                import traceback

                log.error(f"SSE认证: 处理token时发生异常: {str(e)}\n{traceback.format_exc()}")
                # 继续流程，不在这里抛出异常

    # 如果没有通过任何方式获取到current_user，则认证失败
    if current_user is None:
        # 更详细的错误信息，区分不同的认证失败情况
        if token is None:
            log.error("SSE认证: 未能验证用户身份，未提供token参数")
            raise HTTPException(
                code="4001", msg="Authentication failed: token parameter required in URL query for SSE connections"
            )
        elif token == "":
            log.error("SSE认证: 未能验证用户身份，提供了空token参数")
            raise HTTPException(code="4001", msg="Authentication failed: empty token provided")
        else:
            log.error("SSE认证: 未能验证用户身份，提供的token无效")
            raise HTTPException(code="4001", msg="Authentication failed: invalid token")

    return current_user


@router_strm.post("/upload", summary="上传115目录树文件")
async def upload_115_directory_tree_file(current_user: User = Depends(get_current_user), file: UploadFile = File(...)):
    """
    上传并处理115网盘导出的目录树文件 (.txt).
    - **current_user**: 当前已登录用户.
    - **file**: 上传的文件，必须是.txt格式.
    """
    record = await handle_file_upload(file, current_user)
    return Success(data={"filename": record.filename, "path": record.file_path, "record_id": record.id})


@router_strm.post("/upload-url", summary="通过URL上传115目录树文件")
async def upload_115_directory_tree_from_url(
    current_user: User = Depends(get_current_user), data: UrlUploadRequest = Body(...)
):
    """
    通过URL上传并处理115网盘导出的目录树文件 (.txt).
    - **current_user**: 当前已登录用户.
    - **data**: 包含URL地址的请求体.
    """
    from app.log.log import log

    try:
        log.info(f"接收到URL上传请求，用户ID: {current_user.id}, URL: {data.url[:50]}...")
        record = await handle_url_upload(data.url, current_user)
        log.info(f"URL上传成功, 记录ID: {record.id}, 文件名: {record.filename}")
        return Success(data={"filename": record.filename, "path": record.file_path, "record_id": record.id})
    except HTTPException as e:
        log.error(f"URL上传失败，返回HTTP异常, 异常码: {e.code}, 详情: {e.msg}")
        raise
    except Exception as e:
        log.error(f"URL上传过程中发生未处理的异常: {str(e)}", exc_info=True)
        raise HTTPException(code=500, msg=f"URL上传处理失败: {str(e)}")


@router_strm.post("/parse", summary="解析已上传的115目录树文件")
async def parse_directory_tree_file(current_user: User = Depends(get_current_user), data: ParseRequest = Body(...)):
    """
    解析已上传的115网盘导出的目录树文件 (.txt).
    - **current_user**: 当前已登录用户.
    - **data**: 包含上传记录ID的请求体.
    """
    result = await parse_uploaded_file(data.record_id)
    return Success(data=result)


@router_strm.get("/history", summary="获取上传历史记录", response_model=UploadHistoryResponse)
async def get_upload_history(
    current_user: User = Depends(get_current_user),
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
):
    """
    获取当前用户的文件上传历史记录。
    """
    skip = (page - 1) * page_size
    records = await UploadRecord.filter(uploader=current_user).offset(skip).limit(page_size).order_by("-create_time")
    total = await UploadRecord.filter(uploader=current_user).count()

    # 将 ORM 对象转换为字典以便可序列化为 JSON
    record_dicts = []
    for record in records:
        record_dict = {
            "id": record.id,
            "filename": record.filename,
            "filesize": record.filesize,
            "status": record.status.value,
            "create_time": record.create_time.isoformat(),
            "uploader_id": record.uploader_id,
            "parse_time": record.parse_time.isoformat() if record.parse_time else None,
        }
        record_dicts.append(record_dict)

    return Success(data={"total": total, "page": page, "page_size": page_size, "records": record_dicts})


@router_strm.delete("/history/{record_id}", summary="删除上传记录")
async def delete_history_record(
    record_id: int = Path(..., description="记录ID"), current_user: User = Depends(get_current_user)
):
    """
    删除上传记录及对应的文件。
    - **record_id**: 要删除的记录ID.
    - **current_user**: 当前已登录用户.
    """
    await delete_upload_record(record_id, current_user)
    return Success(data={"message": "记录已成功删除"})


@router_strm.get("/download/{record_id}", summary="下载已上传的文件", response_class=FileResponse)
async def download_file(
    request: Request,
    record_id: int = Path(..., description="记录ID"),
    token: Optional[str] = Query(None, description="认证令牌，可通过查询参数传递"),
):
    """
    下载已上传的文件。支持两种认证方式：
    1. 通过Authorization头传递Bearer token (标准方式)
    2. 通过URL查询参数传递token (便于直接下载)

    - **record_id**: 要下载的记录ID.
    - **token**: 可选参数，通过查询参数传递认证令牌.
    """
    # 尝试获取用户
    current_user = None

    # 方法1: 从请求头获取token
    auth_header = request.headers.get("Authorization")
    if auth_header and auth_header.startswith("Bearer "):
        bearer_token = auth_header.replace("Bearer ", "")
        if bearer_token:
            status, code, decode_data = check_token(bearer_token)
            if status:
                if decode_data["data"]["tokenType"] == "accessToken":
                    user_id = decode_data["data"]["userId"]
                    current_user = await User.filter(id=user_id).first()

    # 方法2: 从查询参数获取token
    if current_user is None and token:
        status, code, decode_data = check_token(token)
        if status:
            if decode_data["data"]["tokenType"] == "accessToken":
                user_id = decode_data["data"]["userId"]
                current_user = await User.filter(id=user_id).first()

    # 如果没有通过任何方式获取到current_user，则认证失败
    if current_user is None:
        raise HTTPException(code="4001", msg="Authentication failed, valid token required")

    return await download_upload_file(record_id, current_user)


@router_strm.get("/result/{record_id}", summary="获取文件解析结果")
async def get_file_parse_result(
    record_id: int = Path(..., description="记录ID"),
    current_user: User = Depends(get_current_user),
    file_type: str = Query("all", description="文件类型过滤器 (all, video, audio, image, subtitle, metadata, other)"),
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    all_files: bool = Query(False, description="是否获取所有文件（不分页）"),
):
    """
    获取已解析文件的解析结果，支持按文件类型过滤和分页。
    - **record_id**: 要获取结果的记录ID。
    - **current_user**: 当前已登录用户。
    - **file_type**: 可选的文件类型过滤器，默认为"all"。
    - **page**: 页码，默认为1。
    - **page_size**: 每页数量，默认为10。
    - **all_files**: 是否返回所有文件（不进行分页），默认为false。
    """
    result = await get_parse_result(record_id, current_user, file_type, page, page_size, all_files)
    return Success(data=result)


@router_strm.get("/directory/{record_id}", summary="获取目录内容")
async def get_directory(
    record_id: int = Path(..., description="记录ID"),
    current_user: User = Depends(get_current_user),
    directory_path: str = Query("/", description="目录路径，默认为根目录"),
    file_type: str = Query("all", description="文件类型过滤器 (all, video, audio, image, subtitle, metadata, other)"),
    page: int = Query(1, description="页码"),
    page_size: int = Query(20, description="每页数量"),
):
    """
    获取指定目录下的文件和子目录。
    此API采用懒加载方式，每次只返回指定目录下的直接文件和子目录，不会递归获取所有内容。

    - **record_id**: 要获取结果的记录ID。
    - **current_user**: 当前已登录用户。
    - **directory_path**: 目录路径，默认为根目录"/"。
    - **file_type**: 可选的文件类型过滤器，默认为"all"。
    - **page**: 页码，默认为1。
    - **page_size**: 每页数量，默认为20。
    """
    result = await get_directory_content(record_id, current_user, directory_path, file_type, page, page_size)
    return Success(data=result)


@router_strm.get("/search/{record_id}", summary="搜索文件")
async def search_parsed_files(
    record_id: int = Path(..., description="记录ID"),
    current_user: User = Depends(get_current_user),
    search_text: str = Query(..., description="搜索文本"),
    file_type: str = Query("all", description="文件类型过滤器 (all, video, audio, image, subtitle, metadata, other)"),
    ignore_case: bool = Query(True, description="是否忽略大小写"),
):
    """
    搜索已解析文件中的匹配项
    - **record_id**: 记录ID
    - **current_user**: 当前用户
    - **search_text**: 要搜索的文本
    - **file_type**: 文件类型过滤器 (all, video, audio, image, subtitle, metadata, other)
    - **ignore_case**: 是否忽略大小写，默认为True
    """
    result = await search_files(record_id, current_user, search_text, file_type, ignore_case)
    return Success(data=result)


# === STRM生成相关API端点 ===


@router_strm.get("/servers", summary="获取媒体服务器列表")
async def get_media_servers(current_user: User = Depends(get_current_user)):
    """
    获取当前用户可用的媒体服务器列表
    """
    servers = await MediaServer.all()
    # 将 ORM 对象转换为字典
    server_list = []
    for server in servers:
        server_dict = {
            "id": server.id,
            "name": server.name,
            "server_type": server.server_type,
            "base_url": server.base_url,
            "description": server.description,
            "auth_required": server.auth_required,
            "create_time": server.create_time.isoformat(),
            "status": server.status,
        }
        server_list.append(server_dict)

    return Success(data=server_list)


@router_strm.post("/generate", summary="创建STRM生成任务")
async def generate_strm(
    current_user: User = Depends(get_current_user),
    data: StrmTaskCreate = Body(...),
):
    """
    创建STRM生成任务。
    - **current_user**: 当前已登录用户。
    - **data**: 任务创建数据，包括解析记录ID、服务器ID等。
    """
    from app.log.log import log

    log.info(f"用户 {current_user.user_name} 请求创建STRM生成任务: {data.dict()}")

    try:
        # 检查服务器是否存在 - 执行最小化验证
        from app.models.strm import MediaServer

        server = await MediaServer.get_or_none(id=data.server_id)
        if not server:
            log.error(f"STRM任务创建失败: 找不到ID为{data.server_id}的媒体服务器")
            return Success(
                data={
                    "task_id": None,
                    "status": "failed",
                    "name": "创建失败",
                    "error": f"找不到ID为{data.server_id}的媒体服务器",
                }
            )

        # 如果指定了下载服务器，检查下载服务器是否存在
        if data.download_server_id:
            download_server = await MediaServer.get_or_none(id=data.download_server_id)
            if not download_server:
                log.error(f"STRM任务创建失败: 找不到ID为{data.download_server_id}的下载服务器")
                return Success(
                    data={
                        "task_id": None,
                        "status": "failed",
                        "name": "创建失败",
                        "error": f"找不到ID为{data.download_server_id}的下载服务器",
                    }
                )

        # 快速创建任务记录，最小化操作
        try:
            task = await create_strm_task(
                record_id=data.record_id,
                server_id=data.server_id,
                user=current_user,
                output_dir=data.output_dir,
                custom_name=data.name,
                download_server_id=data.download_server_id,
                download_resources=data.download_resources,
            )
            log.info(f"STRM任务创建成功，ID: {task.id}")
        except Exception as task_error:
            # 详细记录任务创建错误
            import traceback

            error_detail = traceback.format_exc()
            log.error(f"创建STRM任务失败: {str(task_error)}\n{error_detail}")
            return Success(
                data={
                    "task_id": None,
                    "status": "failed",
                    "name": "创建失败",
                    "error": f"创建任务失败: {str(task_error)}",
                }
            )

        # 将任务添加到后台队列，立即返回响应
        # 使用非阻塞方式添加任务
        try:
            from app.core.bgtask import BgTasks

            # 使用更加健壮的方式添加后台任务
            task_added = await BgTasks.add_task(start_strm_task, task.id, current_user.id)

            if not task_added:
                log.error(f"将任务 {task.id} 添加到后台队列失败")
                # 更新任务状态为失败
                task.status = TaskStatus.FAILED
                task.log_content = "将任务添加到后台队列失败"
                await task.save()

                return Success(
                    data={
                        "task_id": task.id,
                        "status": "failed",
                        "name": task.name,
                        "error": "将任务添加到后台队列失败，请稍后重试或联系管理员",
                    }
                )

            log.info(f"已将STRM任务 {task.id} 添加到后台队列")
        except Exception as bg_error:
            # 详细记录后台任务添加错误
            import traceback

            error_detail = traceback.format_exc()
            log.error(f"将任务添加到后台队列失败: {str(bg_error)}\n{error_detail}")

            # 更新任务状态为失败
            task.status = TaskStatus.FAILED
            task.log_content = f"将任务添加到后台队列失败: {str(bg_error)}"
            await task.save()

            return Success(
                data={
                    "task_id": task.id,
                    "status": "failed",
                    "name": task.name,
                    "error": "将任务添加到后台队列失败，请稍后重试或联系管理员",
                }
            )

        # 立即返回任务信息，不等待后台处理
        return Success(
            data={"task_id": task.id, "name": task.name, "status": task.status, "message": "任务已创建，正在后台处理中"}
        )
    except Exception as e:
        import traceback

        error_detail = traceback.format_exc()
        log.error(f"STRM任务创建失败: {str(e)}\n{error_detail}")
        # 返回错误信息，但确保有task_id字段，防止前端出错
        return Success(data={"task_id": None, "status": "failed", "name": "创建失败", "error": str(e)})


@router_strm.get("/tasks", summary="获取用户任务列表")
async def get_tasks(
    current_user: User = Depends(get_current_user),
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    search: Optional[str] = Query(None, description="按名称搜索"),
    status: Optional[str] = Query(None, description="按状态过滤"),
    start_date: Optional[str] = Query(None, description="开始日期过滤 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期过滤 (YYYY-MM-DD)"),
):
    """
    获取当前用户的STRM生成任务列表，支持按名称搜索和按状态/日期过滤
    """
    result = await get_user_tasks(
        current_user, page, page_size, search=search, status=status, start_date=start_date, end_date=end_date
    )
    return Success(data=result)


@router_strm.get("/task/{task_id}", summary="获取任务状态")
async def check_task_status(
    task_id: int = Path(..., description="任务ID"),
    current_user: User = Depends(get_current_user),
):
    """
    获取指定任务的状态和进度
    """
    result = await get_task_status(task_id, current_user)
    return Success(data=result)


@router_strm.post("/task/{task_id}/cancel", summary="取消任务")
async def cancel_task_endpoint(
    task_id: int = Path(..., description="任务ID"),
    current_user: User = Depends(get_current_user),
):
    """
    取消正在进行的任务
    """
    result = await cancel_task(task_id, current_user)
    return Success(data=result)


@router_strm.delete("/task/{task_id}", summary="删除任务")
async def delete_task_endpoint(
    task_id: int = Path(..., description="任务ID"),
    current_user: User = Depends(get_current_user),
):
    """
    删除任务。
    - **task_id**: 任务ID.
    - **current_user**: 当前已登录用户.
    """
    result = await delete_task(task_id, current_user)
    return Success(data=result)


@router_strm.get("/task/{task_id}/logs", summary="获取任务日志")
async def get_task_logs_endpoint(
    task_id: int = Path(..., description="任务ID"),
    current_user: User = Depends(get_current_user),
    page: int = Query(1, description="页码"),
    page_size: int = Query(50, description="每页数量"),
    level: Optional[str] = Query(None, description="日志级别过滤"),
    search: Optional[str] = Query(None, description="日志内容搜索"),
):
    """
    获取任务的详细日志记录。
    - **task_id**: 任务ID.
    - **current_user**: 当前已登录用户.
    - **page**: 页码，默认为1.
    - **page_size**: 每页数量，默认为50.
    - **level**: 日志级别过滤，可选值：INFO、ERROR等.
    - **search**: 日志内容搜索关键词.
    """
    logs = await get_task_logs(task_id, current_user, page, page_size, level, search)
    return Success(data=logs)


@router_strm.get("/download-strm/{task_id}", summary="下载生成的STRM文件", response_class=FileResponse)
async def download_strm_files_endpoint(
    request: Request,
    task_id: int = Path(..., description="任务ID"),
    token: Optional[str] = Query(None, description="认证令牌，可通过查询参数传递"),
):
    """
    下载生成的STRM文件（ZIP压缩包）。支持两种认证方式：
    1. 通过Authorization头传递Bearer token (标准方式)
    2. 通过URL查询参数传递token (便于直接下载)

    - **task_id**: 要下载文件的任务ID.
    - **token**: 可选参数，通过查询参数传递认证令牌.
    """
    # 尝试获取用户
    current_user = None

    # 方法1: 从请求头获取token
    auth_header = request.headers.get("Authorization")
    if auth_header and auth_header.startswith("Bearer "):
        bearer_token = auth_header.replace("Bearer ", "")
        if bearer_token:
            status, code, decode_data = check_token(bearer_token)
            if status:
                if decode_data["data"]["tokenType"] == "accessToken":
                    user_id = decode_data["data"]["userId"]
                    current_user = await User.filter(id=user_id).first()

    # 方法2: 从查询参数获取token
    if current_user is None and token:
        status, code, decode_data = check_token(token)
        if status:
            if decode_data["data"]["tokenType"] == "accessToken":
                user_id = decode_data["data"]["userId"]
                current_user = await User.filter(id=user_id).first()

    # 如果没有通过任何方式获取到current_user，则认证失败
    if current_user is None:
        raise HTTPException(code="4001", msg="认证失败，需要有效的令牌")

    return await download_strm_files(task_id, current_user)


# === 系统设置相关API端点 ===

# === 服务器管理API端点 ===


@router_strm.post("/server", summary="创建媒体服务器")
async def create_media_server(data: MediaServerBase = Body(...), current_user: User = Depends(get_current_user)):
    """
    创建新的媒体服务器

    - **current_user**: 当前已登录用户
    - **data**: 服务器数据
    """
    server = await server_controller.create_server(data.dict())
    return Success(
        data={
            "id": server.id,
            "name": server.name,
            "server_type": server.server_type,
            "base_url": server.base_url,
            "description": server.description,
            "auth_required": server.auth_required,
            "create_time": server.create_time.isoformat(),
            "status": server.status,
        }
    )


@router_strm.put("/server/{server_id}", summary="更新媒体服务器")
async def update_media_server(
    server_id: int = Path(..., description="服务器ID"),
    data: MediaServerBase = Body(...),
    current_user: User = Depends(get_current_user),
):
    """
    更新媒体服务器信息

    - **server_id**: 要更新的服务器ID
    - **data**: 更新的服务器数据
    - **current_user**: 当前已登录用户
    """
    server = await server_controller.update_server(server_id, data.dict())
    return Success(
        data={
            "id": server.id,
            "name": server.name,
            "server_type": server.server_type,
            "base_url": server.base_url,
            "description": server.description,
            "auth_required": server.auth_required,
            "create_time": server.create_time.isoformat(),
            "status": server.status,
        }
    )


@router_strm.delete("/server/{server_id}", summary="删除媒体服务器")
async def delete_media_server(
    server_id: int = Path(..., description="服务器ID"), current_user: User = Depends(get_current_user)
):
    """
    删除媒体服务器

    - **server_id**: 要删除的服务器ID
    - **current_user**: 当前已登录用户
    """
    await server_controller.remove(id=server_id)
    return Success(data={"message": "服务器已成功删除"})


@router_strm.post("/server/{server_id}/test", summary="测试服务器连接")
async def test_server_connection(
    server_id: int = Path(..., description="服务器ID"), current_user: User = Depends(get_current_user)
):
    """
    测试媒体服务器连接

    - **server_id**: 要测试的服务器ID
    - **current_user**: 当前已登录用户
    """
    result = await server_controller.test_connection(server_id)
    return Success(data=result)


@router_strm.post("/server/test", summary="测试未保存的服务器连接")
async def test_server_connection_without_save(
    data: MediaServerBase = Body(...), current_user: User = Depends(get_current_user)
):
    """
    测试未保存的媒体服务器连接

    - **data**: 服务器数据
    - **current_user**: 当前已登录用户
    """
    result = await server_controller.test_connection_without_save(data.dict())
    return Success(data=result)


@router_strm.get("/test-file-type", summary="测试文件类型检测")
async def test_file_type_detection_endpoint(current_user: User = Depends(get_current_user)):
    """
    测试文件类型检测功能，用于验证系统设置是否正确应用于文件类型识别。

    - **current_user**: 当前登录用户，必须拥有管理权限。

    返回不同配置下NFO文件的类型检测结果。
    """
    from app.utils.strm.parser import test_file_type_detection

    # 运行测试并返回结果
    results = await test_file_type_detection()
    return Success(data=results)


@router_strm.get("/task/{task_id}/events", summary="获取任务状态事件流")
async def get_task_events(
    request: Request,
    task_id: int = Path(..., description="任务ID"),
    token: Optional[str] = Query(None, description="认证令牌，可通过查询参数传递"),
):
    """
    获取任务状态的服务器发送事件 (SSE) 流。支持两种认证方式：
    1. 通过Authorization头传递Bearer token
    2. 通过URL查询参数传递token

    - **task_id**: 任务ID
    - **token**: 可选参数，通过查询参数传递认证令牌

    返回一个SSE事件流，客户端可以接收任务状态的实时更新。
    """
    from app.log.log import log
    from fastapi.responses import JSONResponse
    from starlette.status import HTTP_401_UNAUTHORIZED, HTTP_404_NOT_FOUND

    # SSE连接需要特殊处理认证错误，返回适当的HTTP状态码而非默认的200
    try:
        # 检查token是否为空字符串
        if token == "":
            log.warning(f"SSE连接: 任务 {task_id} - 提供了空token参数")
            # 返回401状态码而不是默认的200
            return JSONResponse(
                status_code=HTTP_401_UNAUTHORIZED,
                content={"code": "4001", "msg": "Authentication failed: empty token provided"},
                headers={"X-Request-Type": "SSE-Auth-Failed"},
            )

        # 从请求中获取用户信息
        current_user = await authenticate_user_from_request(request, token)
        if not current_user:
            # 这个条件应该不会触发，因为authenticate_user_from_request会在认证失败时抛出异常
            # 但为了安全起见，我们仍然保留这个检查
            log.warning(f"SSE连接: 任务 {task_id} - 认证失败但未抛出异常")
            return JSONResponse(
                status_code=HTTP_401_UNAUTHORIZED,
                content={"code": "4001", "msg": "Authentication failed"},
                headers={"X-Request-Type": "SSE-Auth-Failed"},
            )

        # 验证任务存在并归属于当前用户
        task = await StrmTask.get_or_none(id=task_id, created_by=current_user)
        if not task:
            log.warning(f"SSE连接: 用户 {current_user.id} 尝试访问不存在或不属于他的任务 {task_id}")
            # 返回404状态码而不是默认的200
            return JSONResponse(
                status_code=HTTP_404_NOT_FOUND,
                content={"code": "4004", "msg": f"Task {task_id} not found or you don't have permission to access it"},
                headers={"X-Request-Type": "SSE-Task-Not-Found"},
            )

        # 记录SSE连接信息
        log.info(f"用户 {current_user.user_name} (ID: {current_user.id}) 已建立任务 {task_id} 的SSE连接")

        # 设置SSE响应头
        headers = {
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",  # Nginx特殊配置，防止缓存
        }

        # 返回SSE事件流
        return EventSourceResponse(
            task_status_event_generator(task_id, current_user.id), headers=headers, media_type="text/event-stream"
        )
    except HTTPException as e:
        # 自定义HTTP异常需要返回适当的状态码，而不是默认的200
        if e.code == "4001":  # 认证错误
            log.warning(f"SSE连接: 任务 {task_id} - 认证失败: {e.msg}")
            return JSONResponse(
                status_code=HTTP_401_UNAUTHORIZED,
                content={"code": e.code, "msg": e.msg},
                headers={"X-Request-Type": "SSE-Auth-Failed"},
            )
        elif e.code == "4004":  # 资源不存在
            log.warning(f"SSE连接: 任务 {task_id} - 资源不存在: {e.msg}")
            return JSONResponse(
                status_code=HTTP_404_NOT_FOUND,
                content={"code": e.code, "msg": e.msg},
                headers={"X-Request-Type": "SSE-Resource-Not-Found"},
            )
        else:
            # 其他异常
            log.error(f"SSE连接: 任务 {task_id} - 异常: 代码 {e.code}, 消息: {e.msg}")
            # 使用对应的HTTP状态码
            status_code = int(e.code) if e.code.isdigit() and len(e.code) == 3 else 500
            return JSONResponse(
                status_code=status_code,
                content={"code": e.code, "msg": e.msg},
                headers={"X-Request-Type": "SSE-Exception"},
            )
    except Exception as e:
        # 捕获所有其他异常
        import traceback

        error_detail = traceback.format_exc()
        log.error(f"创建任务事件流失败: {str(e)}\n{error_detail}")
        return JSONResponse(
            status_code=500,
            content={"code": "5000", "msg": f"Failed to create task event stream: {str(e)}"},
            headers={"X-Request-Type": "SSE-Unknown-Error"},
        )


@router_strm.get("/events/test", summary="SSE连接测试")
async def test_sse_connection(
    request: Request,
    token: Optional[str] = Query(None, description="认证令牌，可通过查询参数传递"),
):
    """
    用于测试SSE连接的简单端点。将返回一个包含连接状态和时间的事件流。

    - **token**: 可选参数，通过查询参数传递认证令牌

    返回一个SSE事件流，包含连接状态、时间和测试消息。
    """
    from app.log.log import log
    from starlette.status import HTTP_401_UNAUTHORIZED

    # SSE测试连接也需要特殊处理认证错误
    try:
        # 检查token是否为空字符串
        if token == "":
            log.warning(f"SSE测试: 提供了空token参数")
            # 返回401状态码而不是默认的200
            return JSONResponse(
                status_code=HTTP_401_UNAUTHORIZED,
                content={"code": "4001", "msg": "Authentication failed: empty token provided"},
                headers={"X-Request-Type": "SSE-Test-Auth-Failed"},
            )

        # 从请求中获取用户信息
        current_user = await authenticate_user_from_request(request, token)
        if not current_user:
            # 这个条件应该不会触发，因为authenticate_user_from_request会在认证失败时抛出异常
            log.warning(f"SSE测试: 认证失败但未抛出异常")
            return JSONResponse(
                status_code=HTTP_401_UNAUTHORIZED,
                content={"code": "4001", "msg": "Authentication failed"},
                headers={"X-Request-Type": "SSE-Test-Auth-Failed"},
            )

        # 设置SSE响应头
        headers = {
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",  # Nginx特殊配置，防止缓存
        }

        # 定义测试事件生成器
        async def test_event_generator():
            log.info(f"SSE测试: 用户 {current_user.user_name} (ID: {current_user.id}) 建立了测试SSE连接")

            # 发送连接建立消息
            yield {
                "event": "connection",
                "data": json.dumps({"status": "established", "timestamp": datetime.now().isoformat()}),
            }

            # 发送初始消息
            yield {"event": "message", "data": json.dumps({"message": "SSE连接测试开始"})}

            # 发送5个测试消息，每个消息间隔2秒
            for i in range(1, 6):
                await asyncio.sleep(2)
                yield {
                    "event": "message",
                    "data": json.dumps({"message": f"测试消息 {i}/5", "timestamp": datetime.now().isoformat()}),
                }
                log.debug(f"SSE测试: 向用户 {current_user.id} 发送测试消息 {i}/5")

            # 发送结束消息
            yield {
                "event": "complete",
                "data": json.dumps({"message": "SSE连接测试完成", "timestamp": datetime.now().isoformat()}),
            }
            log.info(f"SSE测试: 用户 {current_user.id} 的测试SSE连接已完成")

        # 返回SSE事件流
        return EventSourceResponse(test_event_generator(), headers=headers, media_type="text/event-stream")

    except HTTPException as e:
        # 自定义HTTP异常需要返回适当的状态码，而不是默认的200
        if e.code == "4001":  # 认证错误
            log.warning(f"SSE测试连接: 认证失败: {e.msg}")
            return JSONResponse(
                status_code=HTTP_401_UNAUTHORIZED,
                content={"code": e.code, "msg": e.msg},
                headers={"X-Request-Type": "SSE-Test-Auth-Failed"},
            )
        else:
            # 其他异常
            log.error(f"SSE测试连接: 异常: 代码 {e.code}, 消息: {e.msg}")
            # 使用对应的HTTP状态码
            status_code = int(e.code) if e.code.isdigit() and len(e.code) == 3 else 500
            return JSONResponse(
                status_code=status_code,
                content={"code": e.code, "msg": e.msg},
                headers={"X-Request-Type": "SSE-Test-Exception"},
            )
    except Exception as e:
        # 捕获所有其他异常
        import traceback

        error_detail = traceback.format_exc()
        log.error(f"SSE测试连接失败: {str(e)}\n{error_detail}")
        return JSONResponse(
            status_code=500,
            content={"code": "5000", "msg": f"SSE test connection failed: {str(e)}"},
            headers={"X-Request-Type": "SSE-Test-Unknown-Error"},
        )


# 添加WebSocket任务状态事件路由
@router_strm.websocket("/ws/task/{task_id}/events")
async def websocket_task_events(websocket: WebSocket, task_id: int):
    """
    通过WebSocket获取任务状态更新

    支持两种认证方式：
    1. 在请求头中提供 Authorization: Bearer <token> (标准方式)
    2. 在URL查询参数中提供 ?token=<token> (替代方式)
    """
    from app.log.log import log
    from app.models.strm import StrmTask

    # 认证用户
    current_user = await authenticate_websocket(websocket)
    if not current_user:
        # 连接未被接受，客户端将收到关闭帧
        log.warning(f"WebSocket连接: 任务 {task_id} - 认证失败")
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="Authentication failed")
        return

    # 验证任务存在并归属于当前用户
    task = await StrmTask.get_or_none(id=task_id, created_by=current_user)
    if not task:
        log.warning(f"WebSocket连接: 用户 {current_user.id} 尝试访问不存在或不属于他的任务 {task_id}")
        await websocket.close(
            code=status.WS_1008_POLICY_VIOLATION, reason=f"Task {task_id} not found or permission denied"
        )
        return

    # 接受连接
    try:
        log.info(f"用户 {current_user.user_name} (ID: {current_user.id}) 已建立任务 {task_id} 的WebSocket连接")
        await manager.connect(websocket, task_id, current_user.id)

        # 发送初始状态
        task_status = await get_task_status(task_id, current_user)
        await manager.send_json(task_id, current_user.id, task_status, "initial")

        # 启动任务状态事件生成器
        task_status_generator = task_status_event_generator(task_id, current_user.id)

        # 心跳计数器
        heartbeat_counter = 0

        # 保持连接并转发事件
        while True:
            # 监听客户端消息
            try:
                # 非阻塞方式接收消息，超时3秒
                data = await websocket.receive_json()
                if data.get("type") == "ping":
                    await manager.send_json(task_id, current_user.id, {"type": "pong"})
                    continue
            except WebSocketDisconnect:
                log.info(f"WebSocket连接: 用户 {current_user.id} 断开连接 (任务: {task_id})")
                manager.disconnect(task_id, current_user.id)
                break
            except Exception:
                # 可能是接收超时，继续处理
                pass

            # 每20次循环发送一次心跳
            heartbeat_counter += 1
            if heartbeat_counter >= 20:
                await manager.send_json(
                    task_id, current_user.id, {"timestamp": datetime.now().isoformat()}, "heartbeat"
                )
                heartbeat_counter = 0

            # 检查是否有任务状态更新
            try:
                log.debug(f"WebSocket: 任务 {task_id} - 尝试从状态生成器获取事件")
                event = await anext(task_status_generator)
                if event:
                    event_type = event.get("event", "update")
                    log.debug(f"WebSocket: 任务 {task_id} - 收到事件类型: {event_type}")
                    data = json.loads(event.get("data", "{}"))
                    log.debug(
                        f"WebSocket: 任务 {task_id} - 发送事件到客户端: 类型={event_type}, 数据长度={len(str(data))}"
                    )
                    result = await manager.send_json(task_id, current_user.id, data, event_type)
                    log.debug(f"WebSocket: 任务 {task_id} - 事件发送结果: {result}")

                    # 如果是完成事件，关闭连接
                    if event_type == "complete":
                        log.info(f"任务 {task_id} 已完成，关闭WebSocket连接")
                        await websocket.close(code=status.WS_1000_NORMAL_CLOSURE)
                        manager.disconnect(task_id, current_user.id)
                        break
                else:
                    log.debug(f"WebSocket: 任务 {task_id} - 从生成器获取的事件为空")
            except StopAsyncIteration:
                # 生成器已结束，但保持连接
                log.debug(f"WebSocket: 任务 {task_id} - 生成器已结束 (StopAsyncIteration)")
                pass
            except Exception as e:
                import traceback

                log.error(f"处理任务状态事件失败: {str(e)}")
                log.debug(f"WebSocket错误详情: {traceback.format_exc()}")

            # 短暂休眠，避免CPU占用过高
            await asyncio.sleep(0.5)

    except WebSocketDisconnect:
        log.info(f"WebSocket连接: 用户 {current_user.id} 断开连接 (任务: {task_id})")
        manager.disconnect(task_id, current_user.id)
    except Exception as e:
        import traceback

        log.error(f"WebSocket处理异常: {str(e)}\n{traceback.format_exc()}")
        manager.disconnect(task_id, current_user.id)


# WebSocket测试路由
@router_strm.websocket("/ws/test")
async def websocket_test(websocket: WebSocket):
    """
    WebSocket测试连接，用于验证配置是否正确

    支持两种认证方式：
    1. 在请求头中提供 Authorization: Bearer <token> (标准方式)
    2. 在URL查询参数中提供 ?token=<token> (替代方式)
    """
    from app.log.log import log

    # 认证用户
    current_user = await authenticate_websocket(websocket)
    if not current_user:
        log.warning(f"WebSocket测试连接: 认证失败")
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="Authentication failed")
        return

    # 接受连接
    try:
        await websocket.accept()
        log.info(f"用户 {current_user.user_name} (ID: {current_user.id}) 已建立WebSocket测试连接")

        # 发送连接成功消息
        await websocket.send_json(
            {
                "event": "connection",
                "data": {
                    "status": "established",
                    "timestamp": datetime.now().isoformat(),
                    "user": current_user.user_name,
                },
            }
        )

        # 发送5个测试消息
        for i in range(1, 6):
            await asyncio.sleep(1)
            await websocket.send_json(
                {"event": "message", "data": {"message": f"测试消息 {i}/5", "timestamp": datetime.now().isoformat()}}
            )

        # 发送完成消息
        await websocket.send_json(
            {"event": "complete", "data": {"message": "WebSocket连接测试完成", "timestamp": datetime.now().isoformat()}}
        )

        # 保持连接30秒后关闭
        await asyncio.sleep(5)
        await websocket.close(code=status.WS_1000_NORMAL_CLOSURE)

    except WebSocketDisconnect:
        log.info(f"WebSocket测试连接: 用户 {current_user.id} 断开连接")
    except Exception as e:
        import traceback

        log.error(f"WebSocket测试连接异常: {str(e)}\n{traceback.format_exc()}")
